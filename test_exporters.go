package main

import (
	"context"
	"fmt"
	"log"
	"time"
	"wnapi/internal/pkg/tracing"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
)

func main() {
	fmt.Println("🔍 Testing OpenTelemetry Tracing with Multiple Exporters...")

	// Test Console Exporter
	testConsoleExporter()

	// Test OTLP Exporter (will fail if no collector, but should not crash)
	testOTLPExporter()

	// Test Jaeger Exporter (will fail if no <PERSON><PERSON><PERSON>, but should not crash)
	testJaegerExporter()

	fmt.Println("✅ All exporter tests completed!")
}

func testConsoleExporter() {
	fmt.Println("\n1️⃣ Testing Console Exporter...")
	
	config := &tracing.Config{
		ServiceName:    "wnapi-test-console",
		ServiceVersion: "1.0.0",
		Environment:    "test",
		ExporterType:   "console",
		Enabled:        true,
	}

	manager, err := tracing.NewManager(config)
	if err != nil {
		log.Printf("❌ Failed to create console manager: %v", err)
		return
	}

	if err := manager.Initialize(); err != nil {
		log.Printf("❌ Failed to initialize console tracing: %v", err)
		return
	}

	// Create a test span
	tracer := otel.Tracer("test-console")
	_, span := tracer.Start(context.Background(), "console-test-operation")
	span.SetAttributes(
		attribute.String("test.type", "console"),
		attribute.String("exporter", "console"),
	)
	
	// Simulate some work
	time.Sleep(100 * time.Millisecond)
	span.AddEvent("Console test event")
	span.End()

	// Force flush and shutdown
	if err := manager.Shutdown(context.Background()); err != nil {
		log.Printf("❌ Failed to shutdown console tracing: %v", err)
	} else {
		fmt.Println("✅ Console exporter test completed successfully")
	}
}

func testOTLPExporter() {
	fmt.Println("\n2️⃣ Testing OTLP Exporter...")
	
	config := &tracing.Config{
		ServiceName:    "wnapi-test-otlp",
		ServiceVersion: "1.0.0",
		Environment:    "test",
		ExporterType:   "otlp",
		Enabled:        true,
		OTLP: tracing.OTLPConfig{
			Endpoint: "http://localhost:4317",
			Insecure: true,
			Timeout:  5 * time.Second,
		},
	}

	manager, err := tracing.NewManager(config)
	if err != nil {
		log.Printf("❌ Failed to create OTLP manager: %v", err)
		return
	}

	if err := manager.Initialize(); err != nil {
		log.Printf("⚠️ OTLP exporter failed to initialize (expected if no collector): %v", err)
		return
	}

	// Create a test span
	tracer := otel.Tracer("test-otlp")
	_, span := tracer.Start(context.Background(), "otlp-test-operation")
	span.SetAttributes(
		attribute.String("test.type", "otlp"),
		attribute.String("exporter", "otlp"),
	)
	
	time.Sleep(100 * time.Millisecond)
	span.AddEvent("OTLP test event")
	span.End()

	if err := manager.Shutdown(context.Background()); err != nil {
		log.Printf("❌ Failed to shutdown OTLP tracing: %v", err)
	} else {
		fmt.Println("✅ OTLP exporter test completed successfully")
	}
}

func testJaegerExporter() {
	fmt.Println("\n3️⃣ Testing Jaeger Exporter...")
	
	config := &tracing.Config{
		ServiceName:    "wnapi-test-jaeger",
		ServiceVersion: "1.0.0",
		Environment:    "test",
		ExporterType:   "jaeger",
		Enabled:        true,
		Jaeger: tracing.JaegerConfig{
			Endpoint: "http://localhost:14268/api/traces",
		},
	}

	manager, err := tracing.NewManager(config)
	if err != nil {
		log.Printf("❌ Failed to create Jaeger manager: %v", err)
		return
	}

	if err := manager.Initialize(); err != nil {
		log.Printf("⚠️ Jaeger exporter failed to initialize (expected if no Jaeger): %v", err)
		return
	}

	// Create a test span
	tracer := otel.Tracer("test-jaeger")
	_, span := tracer.Start(context.Background(), "jaeger-test-operation")
	span.SetAttributes(
		attribute.String("test.type", "jaeger"),
		attribute.String("exporter", "jaeger"),
	)
	
	time.Sleep(100 * time.Millisecond)
	span.AddEvent("Jaeger test event")
	span.End()

	if err := manager.Shutdown(context.Background()); err != nil {
		log.Printf("❌ Failed to shutdown Jaeger tracing: %v", err)
	} else {
		fmt.Println("✅ Jaeger exporter test completed successfully")
	}
}
