package main

import (
	"context"
	"fmt"
	"log"
	"time"
	"wnapi/internal/pkg/tracing"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/trace"
)

func main() {
	fmt.Println("Testing OpenTelemetry Tracing Integration...")

	// Initialize tracing
	manager := tracing.NewManager(&tracing.Config{
		ServiceName:    "wnapi-test",
		ServiceVersion: "1.0.0",
		Environment:    "test",
		ExporterType:   "console", // Use console exporter for testing
		OTLP: tracing.OTLPConfig{
			Endpoint: "http://localhost:4317",
			Insecure: true,
		},
	})

	// Start tracing
	if err := manager.Start(context.Background()); err != nil {
		log.Fatalf("Failed to start tracing: %v", err)
	}
	defer func() {
		if err := manager.Shutdown(context.Background()); err != nil {
			log.Printf("Failed to shutdown tracing: %v", err)
		}
	}()

	// Test basic tracing functionality
	testBasicTracing()
	testModuleTracing()
	testErrorTracing()

	fmt.Println("All tracing tests completed successfully!")
}

func testBasicTracing() {
	fmt.Println("\n1. Testing basic tracing...")

	tracer := otel.Tracer("test")
	ctx, span := tracer.Start(context.Background(), "test_operation")
	defer span.End()

	span.SetAttributes(
		attribute.String("test.type", "basic"),
		attribute.String("test.description", "Basic tracing functionality"),
	)

	// Simulate some work
	time.Sleep(100 * time.Millisecond)

	span.AddEvent("Basic test completed")
	fmt.Println("✓ Basic tracing test completed")
}

func testModuleTracing() {
	fmt.Println("\n2. Testing module-specific tracing...")

	ctx := context.Background()

	// Test auth module tracing
	ctx = tracing.StartSpan(ctx, "auth.register",
		tracing.Module("auth"),
		tracing.Function("register"),
		attribute.String("user.username", "testuser"),
		attribute.String("user.email", "<EMAIL>"),
	)

	span := trace.SpanFromContext(ctx)
	defer span.End()

	// Simulate auth processing
	time.Sleep(50 * time.Millisecond)

	span.AddEvent("User validation completed")
	span.SetAttributes(attribute.Bool("validation.success", true))

	fmt.Println("✓ Module-specific tracing test completed")
}

func testErrorTracing() {
	fmt.Println("\n3. Testing error tracing...")

	tracer := otel.Tracer("test")
	ctx, span := tracer.Start(context.Background(), "test_error_operation")
	defer span.End()

	// Simulate an error
	err := fmt.Errorf("test error for tracing")
	tracing.RecordError(ctx, err,
		attribute.String("error.type", "test"),
		attribute.String("error.context", "testing error recording"),
	)

	fmt.Println("✓ Error tracing test completed")
}
