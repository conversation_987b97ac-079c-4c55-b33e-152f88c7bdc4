package mysql

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/webnew/wn-backend-v2/modules/auth/models"
)

type authRepository struct {
	db *sql.DB
}

func NewAuthRepository(db *sql.DB) *authRepository {
	return &authRepository{db: db}
}

func (r *authRepository) Create(ctx context.Context, session *models.Session) error {
	query := `
		INSERT INTO auth_sessions (id, user_id, access_token, refresh_token, expires_at)
		VALUES (?, ?, ?, ?, ?)
	`
	_, err := r.db.ExecContext(ctx, query,
		session.ID,
		session.UserID,
		session.AccessToken,
		session.RefreshToken,
		session.ExpiresAt,
	)
	if err != nil {
		return fmt.Errorf("failed to create session: %w", err)
	}
	return nil
}

func (r *authRepository) Get(ctx context.Context, id string) (*models.Session, error) {
	query := `
		SELECT id, user_id, access_token, refresh_token, expires_at
		FROM auth_sessions
		WHERE id = ?
	`
	var session models.Session
	err := r.db.QueryRowContext(ctx, query, id).Scan(
		&session.ID,
		&session.UserID,
		&session.AccessToken,
		&session.RefreshToken,
		&session.ExpiresAt,
	)
	if err == sql.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		return nil, fmt.Errorf("failed to get session: %w", err)
	}
	return &session, nil
}

func (r *authRepository) Delete(ctx context.Context, id string) error {
	query := "DELETE FROM auth_sessions WHERE id = ?"
	_, err := r.db.ExecContext(ctx, query, id)
	if err != nil {
		return fmt.Errorf("failed to delete session: %w", err)
	}
	return nil
}
