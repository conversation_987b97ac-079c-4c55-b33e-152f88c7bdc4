package repository

import (
	"context"
	"database/sql"
	"errors"
	"time"
	"wnapi/internal/database"
	"wnapi/internal/pkg/logger"
	"wnapi/modules/auth/internal"

	"github.com/google/uuid"
	"github.com/jmoiron/sqlx"
	"golang.org/x/crypto/bcrypt"
)

// MySQL errors
var (
	ErrUserNotFound    = errors.New("user not found")
	ErrTokenNotFound   = errors.New("token not found")
	ErrDuplicateUser   = errors.New("user already exists")
	ErrInvalidPassword = errors.New("invalid password")
)

// MySQLRepository triển khai Repository interface với MySQL
type MySQLRepository struct {
	db     *sqlx.DB
	logger logger.Logger
}

// NewMySQLRepository tạo một MySQLRepository mới
func NewMySQLRepository(dbManager *database.Manager, log logger.Logger) (internal.Repository, error) {
	return &MySQLRepository{
		db:     dbManager.DB,
		logger: log,
	}, nil
}

// CreateUser tạo người dùng mới
func (r *MySQLRepository) CreateUser(ctx context.Context, user *internal.User, password string) error {
	// Hash password
	passwordHash, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		r.logger.Error("Failed to hash password", logger.String("error", err.Error()))
		return err
	}

	user.PasswordHash = string(passwordHash)

	query := `INSERT INTO users (username, email, password_hash, full_name, is_active) 
			  VALUES (?, ?, ?, ?, ?)`

	result, err := r.db.ExecContext(ctx, query, user.Username, user.Email, user.PasswordHash, user.FullName, user.IsActive)
	if err != nil {
		r.logger.Error("Failed to create user", logger.String("error", err.Error()))
		return err
	}

	id, err := result.LastInsertId()
	if err != nil {
		r.logger.Error("Failed to get last insert ID", logger.String("error", err.Error()))
		return err
	}

	user.ID = int(id)
	return nil
}

// GetUserByID lấy người dùng theo ID
func (r *MySQLRepository) GetUserByID(ctx context.Context, id int) (*internal.User, error) {
	user := &internal.User{}

	query := `SELECT * FROM users WHERE id = ?`

	err := r.db.GetContext(ctx, user, query, id)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, internal.ErrUserNotFound
		}
		r.logger.Error("Failed to get user by ID", logger.String("error", err.Error()))
		return nil, err
	}

	return user, nil
}

// GetUserByUsername lấy người dùng theo username
func (r *MySQLRepository) GetUserByUsername(ctx context.Context, username string) (*internal.User, error) {
	user := &internal.User{}

	query := `SELECT * FROM users WHERE username = ?`

	err := r.db.GetContext(ctx, user, query, username)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, internal.ErrUserNotFound
		}
		r.logger.Error("Failed to get user by username", logger.String("error", err.Error()))
		return nil, err
	}

	return user, nil
}

// GetUserByEmail lấy người dùng theo email
func (r *MySQLRepository) GetUserByEmail(ctx context.Context, email string) (*internal.User, error) {
	user := &internal.User{}

	query := `SELECT * FROM users WHERE email = ?`

	err := r.db.GetContext(ctx, user, query, email)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, internal.ErrUserNotFound
		}
		r.logger.Error("Failed to get user by email", logger.String("error", err.Error()))
		return nil, err
	}

	return user, nil
}

// UpdateUser cập nhật thông tin người dùng
func (r *MySQLRepository) UpdateUser(ctx context.Context, user *internal.User) error {
	query := `UPDATE users 
			  SET username = ?, email = ?, password_hash = ?, full_name = ?, is_active = ?
			  WHERE id = ?`

	_, err := r.db.ExecContext(ctx, query, user.Username, user.Email, user.PasswordHash,
		user.FullName, user.IsActive, user.ID)
	if err != nil {
		r.logger.Error("Failed to update user", logger.String("error", err.Error()))
		return err
	}

	return nil
}

// DeleteUser xóa người dùng
func (r *MySQLRepository) DeleteUser(ctx context.Context, id int) error {
	query := `DELETE FROM users WHERE id = ?`

	_, err := r.db.ExecContext(ctx, query, id)
	if err != nil {
		r.logger.Error("Failed to delete user", logger.String("error", err.Error()))
		return err
	}

	return nil
}

// CreateToken tạo token mới
func (r *MySQLRepository) CreateToken(ctx context.Context, token *internal.Token) error {
	// Nếu token chưa có ID, tạo UUID mới
	if token.ID == "" {
		token.ID = uuid.NewString()
	}

	query := `INSERT INTO tokens (id, user_id, token_type, token_value, expires_at) 
			  VALUES (?, ?, ?, ?, ?)`

	_, err := r.db.ExecContext(ctx, query, token.ID, token.UserID, token.TokenType,
		token.Value, token.ExpiresAt)
	if err != nil {
		r.logger.Error("Failed to create token", logger.String("error", err.Error()))
		return err
	}

	return nil
}

// GetTokenByValue lấy token theo giá trị và loại
func (r *MySQLRepository) GetTokenByValue(ctx context.Context, tokenValue string, tokenType internal.TokenType) (*internal.Token, error) {
	token := &internal.Token{}

	query := `SELECT * FROM tokens WHERE token_value = ? AND token_type = ?`

	err := r.db.GetContext(ctx, token, query, tokenValue, tokenType)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, internal.ErrTokenNotFound
		}
		r.logger.Error("Failed to get token", logger.String("error", err.Error()))
		return nil, err
	}

	return token, nil
}

// DeleteToken xóa token
func (r *MySQLRepository) DeleteToken(ctx context.Context, id string) error {
	query := `DELETE FROM tokens WHERE id = ?`

	_, err := r.db.ExecContext(ctx, query, id)
	if err != nil {
		r.logger.Error("Failed to delete token", logger.String("error", err.Error()))
		return err
	}

	return nil
}

// DeleteExpiredTokens xóa các token đã hết hạn
func (r *MySQLRepository) DeleteExpiredTokens(ctx context.Context) error {
	query := `DELETE FROM tokens WHERE expires_at < ?`

	now := time.Now()
	_, err := r.db.ExecContext(ctx, query, now)
	if err != nil {
		r.logger.Error("Failed to delete expired tokens", logger.String("error", err.Error()))
		return err
	}

	return nil
}
