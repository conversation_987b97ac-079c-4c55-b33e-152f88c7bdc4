package domain

import (
	"context"
	"errors"
	"fmt"
	"time"
	"wnapi/internal/pkg/logger"
	"wnapi/modules/auth/dto"
	"wnapi/modules/auth/internal"

	"github.com/golang-jwt/jwt/v4"
	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
)

// Service triển khai AuthService interface
type Service struct {
	repo   internal.Repository
	config internal.AuthConfig
	logger logger.Logger
}

// NewService tạo một auth service mới
func NewService(repo internal.Repository, config internal.AuthConfig, log logger.Logger) internal.AuthService {
	// Đặt giá trị mặc định
	if config.AccessTokenExpiry == 0 {
		config.AccessTokenExpiry = 15 * time.Minute
	}
	if config.RefreshTokenExpiry == 0 {
		config.RefreshTokenExpiry = 7 * 24 * time.Hour // 7 ngày
	}

	return &Service{
		repo:   repo,
		config: config,
		logger: log,
	}
}

// Register đăng ký người dùng mới
func (s *Service) Register(ctx context.Context, req dto.RegisterRequest) (*internal.UserInfo, error) {
	// Kiểm tra username đã tồn tại chưa
	_, err := s.repo.GetUserByUsername(ctx, req.Username)
	if err == nil {
		return nil, internal.ErrUserAlreadyExists
	} else if !errors.Is(err, internal.ErrUserNotFound) {
		s.logger.Error("Failed to check existing user", logger.String("error", err.Error()))
		return nil, err
	}

	// Kiểm tra email đã tồn tại chưa
	if req.Email != "" {
		_, err = s.repo.GetUserByEmail(ctx, req.Email)
		if err == nil {
			return nil, internal.ErrUserAlreadyExists
		} else if !errors.Is(err, internal.ErrUserNotFound) {
			s.logger.Error("Failed to check existing email", logger.String("error", err.Error()))
			return nil, err
		}
	}

	// Tạo user mới
	user := &internal.User{
		Username: req.Username,
		Email:    req.Email,
		FullName: req.FullName,
		IsActive: true,
	}

	// Lưu vào database
	if err := s.repo.CreateUser(ctx, user, req.Password); err != nil {
		s.logger.Error("Failed to create user", logger.String("error", err.Error()))
		return nil, err
	}

	// Trả về thông tin user
	return &internal.UserInfo{
		ID:       user.ID,
		Username: user.Username,
		Email:    user.Email,
	}, nil
}

// Login xác thực người dùng và trả về token
func (s *Service) Login(ctx context.Context, req dto.LoginRequest) (*dto.LoginResponse, error) {
	// Lấy user từ database bằng email (theo DTO mới)
	user, err := s.repo.GetUserByEmail(ctx, req.Email)
	if err != nil {
		if errors.Is(err, internal.ErrUserNotFound) {
			return nil, internal.ErrInvalidCredentials
		}
		s.logger.Error("Failed to get user", logger.String("error", err.Error()))
		return nil, err
	}

	// Kiểm tra password
	if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(req.Password)); err != nil {
		s.logger.Warn("Invalid password attempt", logger.String("email", req.Email))
		return nil, internal.ErrInvalidCredentials
	}

	// Tạo JWT token
	accessToken, _, err := s.generateJWTToken(user.ID, user.Username, s.config.AccessTokenExpiry)
	if err != nil {
		s.logger.Error("Failed to generate access token", logger.String("error", err.Error()))
		return nil, err
	}

	// Tạo refresh token
	refreshToken := uuid.NewString()
	refreshExpiry := time.Now().Add(s.config.RefreshTokenExpiry)

	// Lưu refresh token vào database
	token := &internal.Token{
		UserID:    user.ID,
		TokenType: internal.TokenTypeRefresh,
		Value:     refreshToken,
		ExpiresAt: refreshExpiry,
	}

	if err := s.repo.CreateToken(ctx, token); err != nil {
		s.logger.Error("Failed to save refresh token", logger.String("error", err.Error()))
		return nil, err
	}

	// Trả về response
	return &dto.LoginResponse{
		AccessToken:           accessToken,
		AccessTokenExpiresIn:  int(s.config.AccessTokenExpiry.Seconds()),
		RefreshToken:          refreshToken,
		RefreshTokenExpiresIn: int(s.config.RefreshTokenExpiry.Seconds()),
		TokenType:             "Bearer",
		UserID:                int64(user.ID),
		Email:                 user.Email,
		TenantID:              0, // TODO: Add tenant support when available
	}, nil
}

// RefreshToken làm mới access token
func (s *Service) RefreshToken(ctx context.Context, refreshToken string) (*dto.LoginResponse, error) {
	// Lấy token từ database
	token, err := s.repo.GetTokenByValue(ctx, refreshToken, internal.TokenTypeRefresh)
	if err != nil {
		if errors.Is(err, internal.ErrTokenNotFound) {
			return nil, internal.ErrInvalidToken
		}
		s.logger.Error("Failed to get refresh token", logger.String("error", err.Error()))
		return nil, err
	}

	// Kiểm tra token có hết hạn không
	if token.IsExpired() {
		// Xóa token đã hết hạn
		if err := s.repo.DeleteToken(ctx, token.ID); err != nil {
			s.logger.Error("Failed to delete expired token", logger.String("error", err.Error()))
		}
		return nil, internal.ErrExpiredToken
	}

	// Lấy user từ database
	user, err := s.repo.GetUserByID(ctx, token.UserID)
	if err != nil {
		s.logger.Error("Failed to get user for refresh token", logger.String("error", err.Error()))
		return nil, err
	}

	// Tạo access token mới
	accessToken, _, err := s.generateJWTToken(user.ID, user.Username, s.config.AccessTokenExpiry)
	if err != nil {
		s.logger.Error("Failed to generate new access token", logger.String("error", err.Error()))
		return nil, err
	}

	// Trả về response
	return &dto.LoginResponse{
		AccessToken:           accessToken,
		AccessTokenExpiresIn:  int(s.config.AccessTokenExpiry.Seconds()),
		RefreshToken:          refreshToken, // Giữ nguyên refresh token
		RefreshTokenExpiresIn: int(s.config.RefreshTokenExpiry.Seconds()),
		TokenType:             "Bearer",
		UserID:                int64(user.ID),
		Email:                 user.Email,
		TenantID:              0, // TODO: Add tenant support when available
	}, nil
}

// ValidateToken kiểm tra tính hợp lệ của token
func (s *Service) ValidateToken(tokenString string) (map[string]interface{}, error) {
	// Parse token
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		// Kiểm tra signing method
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(s.config.JWTSecret), nil
	})

	if err != nil {
		return nil, err
	}

	// Kiểm tra tính hợp lệ
	if !token.Valid {
		return nil, internal.ErrInvalidToken
	}

	// Lấy claims
	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, internal.ErrInvalidToken
	}

	return claims, nil
}

// generateJWTToken tạo JWT token
func (s *Service) generateJWTToken(userID int, username string, expiry time.Duration) (string, time.Time, error) {
	expiryTime := time.Now().Add(expiry)

	// Tạo token claims
	claims := jwt.MapClaims{
		"sub":  fmt.Sprintf("%d", userID),
		"user": username,
		"exp":  expiryTime.Unix(),
		"iat":  time.Now().Unix(),
		"jti":  uuid.NewString(),
	}

	// Tạo token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// Ký token
	tokenString, err := token.SignedString([]byte(s.config.JWTSecret))
	if err != nil {
		return "", time.Time{}, err
	}

	return tokenString, expiryTime, nil
}
