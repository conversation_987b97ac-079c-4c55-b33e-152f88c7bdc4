package models

import (
	"time"
)

type Session struct {
	ID           string    `db:"id" json:"id" gorm:"primaryKey"`
	UserID       int64     `db:"user_id" json:"user_id"`
	AccessToken  string    `db:"access_token" json:"access_token"`
	RefreshToken string    `db:"refresh_token" json:"refresh_token"`
	ExpiresAt    time.Time `db:"expires_at" json:"expires_at"`
	CreatedAt    time.Time `db:"created_at" json:"created_at"`
	UpdatedAt    time.Time `db:"updated_at" json:"updated_at"`
}

// TableName xác định tên bảng trong cơ sở dữ liệu
func (Session) TableName() string {
	return "auth_sessions"
}
