package service

import (
	"context"
	"errors"
	"fmt"
	"log"
	"regexp"
	"strings"
	"time"

	"wnapi/modules/auth/client"
	"wnapi/modules/auth/dto/request"
	"wnapi/modules/auth/dto/response"
	"wnapi/modules/auth/models"
	"wnapi/modules/auth/repository"
	authEvent "wnapi/modules/auth/service/event"
	"wnapi/modules/auth/tracing"
	"wnapi/pkg/auth"
	notificationProto "wnapi/pkg/proto/notification"
	userProto "wnapi/pkg/proto/user"

	"google.golang.org/grpc"

	"github.com/google/uuid"
)

// Định nghĩa các error codes
var (
	ErrInvalidCredentials  = errors.New("thông tin đăng nhập không hợp lệ")
	ErrEmailNotVerified    = errors.New("email chưa được xác thực")
	ErrSessionNotFound     = errors.New("phiên đăng nhập không tồn tại hoặc đã hết hạn")
	ErrInvalidRefreshToken = errors.New("refresh token không hợp lệ hoặc đã hết hạn")
	ErrIncorrectPassword   = errors.New("mật khẩu hiện tại không chính xác")
	ErrSamePassword        = errors.New("mật khẩu mới không được giống mật khẩu hiện tại")
	ErrUserNotFound        = errors.New("không tìm thấy người dùng với email này")
	ErrInvalidUserType     = errors.New("loại người dùng không hợp lệ cho hệ thống này")
	ErrInvalidToken        = errors.New("token không hợp lệ")
	ErrTokenExpired        = errors.New("token đã hết hạn")
)

// Constants for tracing attributes
const (
	AttrUserEmailDomain  = "user.email_domain"
	AttrUserTenantID     = "user.tenant_id"
	AttrUserID           = "user.id"
	AttrUserStatus       = "user.status"
	AttrAuthFailedReason = "auth.failed_reason"
	AttrAuthSuccess      = "auth.success"
)

// AuthService định nghĩa service xử lý xác thực người dùng
type AuthService struct {
	sessionRepo           repository.SessionRepository
	emailVerificationRepo repository.EmailVerificationRepository
	jwtService            *auth.JWTService
	userClient            userProto.UserServiceClient
	notificationClient    notificationProto.NotificationServiceClient
	tenantClient          *client.TenantClient
	baseURL               string
	webURL                string
	tracer                *tracing.TracerWrapper
	rbacClient            *client.RBACClient
	eventPublisher        *authEvent.AuthEventPublisher
	logger                *log.Logger
}

// NewAuthService tạo instance mới của AuthService
func NewAuthService(
	sessionRepo repository.SessionRepository,
	emailVerificationRepo repository.EmailVerificationRepository,
	jwtService *auth.JWTService,
	userConn *grpc.ClientConn,
	notificationConn *grpc.ClientConn,
	baseURL string,
	webURL string,
	tracer *tracing.TracerWrapper,
	rbacBaseURL string,
) *AuthService {
	logger := log.New(log.Writer(), "[AUTH_SERVICE] ", log.LstdFlags|log.Lshortfile)

	// Khởi tạo tenant client
	var tenantClient *client.TenantClient
	var err error
	if baseURL != "" {
		tenantClient, err = client.NewTenantClient(baseURL)
		if err != nil {
			logger.Printf("Cảnh báo: Không thể tạo tenant client: %v", err)
		}
	}

	// Khởi tạo RBAC client
	var rbacClient *client.RBACClient
	if rbacBaseURL != "" {
		rbacClient, err = client.NewRBACClient(rbacBaseURL)
		if err != nil {
			logger.Printf("Cảnh báo: Không thể tạo RBAC client: %v", err)
		}
	}

	return &AuthService{
		sessionRepo:           sessionRepo,
		emailVerificationRepo: emailVerificationRepo,
		jwtService:            jwtService,
		userClient:            userProto.NewUserServiceClient(userConn),
		notificationClient:    notificationProto.NewNotificationServiceClient(notificationConn),
		tenantClient:          tenantClient,
		baseURL:               baseURL,
		webURL:                webURL,
		tracer:                tracer,
		rbacClient:            rbacClient,
		eventPublisher:        nil, // Sẽ cập nhật sau khi event được thiết lập
		logger:                logger,
	}
}

// SetEventPublisher thiết lập event publisher cho service
func (s *AuthService) SetEventPublisher(publisher *authEvent.AuthEventPublisher) {
	s.eventPublisher = publisher
	s.logger.Printf("Event publisher đã được thiết lập cho AuthService")
}

// Login xử lý đăng nhập người dùng
func (s *AuthService) Login(ctx context.Context, req *request.LoginRequest) (*response.AuthTokenResponse, error) {
	// Tạo context với timeout
	ctxWithTimeout, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	// Gọi user service để xác thực
	userResp, err := s.userClient.GetUserByCredentials(ctxWithTimeout, &userProto.GetUserByCredentialsRequest{
		Email:    req.Email,
		Password: req.Password,
	})
	if err != nil {
		return nil, ErrInvalidCredentials
	}

	// Kiểm tra trạng thái xác thực email
	if userResp.Status == "inactive" || !userResp.IsEmailVerified {
		return nil, ErrEmailNotVerified
	}

	// Kiểm tra loại người dùng nếu đang đăng nhập vào hệ thống admin
	if req.AdminLogin {
		// Chỉ cho phép user_type là admin hoặc tenant đăng nhập vào hệ thống admin
		if userResp.UserType != userProto.UserType_ADMIN && userResp.UserType != userProto.UserType_TENANT {
			return nil, ErrInvalidUserType
		}
	} else {
		// Chỉ cho phép user_type là customer đăng nhập vào hệ thống frontend
		if userResp.UserType != userProto.UserType_CUSTOMER {
			return nil, ErrInvalidUserType
		}
	}

	// Tạo JWT tokens
	accessToken, refreshToken, err := s.jwtService.GenerateTokenPair(uint(userResp.UserId), 1, userResp.Email, "user")
	if err != nil {
		return nil, fmt.Errorf("không thể tạo tokens: %w", err)
	}

	// Lưu session
	session := &models.Session{
		ID:           uuid.New().String(),
		UserID:       userResp.UserId,
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresAt:    time.Now().Add(s.jwtService.GetConfig().RefreshTokenExpiration),
	}

	// Tạo context với timeout cho session creation
	sessionCtx, sessionCancel := context.WithTimeout(ctx, 5*time.Second)
	defer sessionCancel()

	if err := s.sessionRepo.Create(sessionCtx, session); err != nil {
		return nil, err
	}

	// Lấy thông tin IP và User-Agent (trong thực tế sẽ lấy từ context)
	ip := "127.0.0.1"
	userAgent := "User Agent Example"

	// Xuất bản event nếu có publisher
	if s.eventPublisher != nil {
		if err := s.eventPublisher.PublishUserLoggedIn(ctx, int(userResp.UserId), req.Email, 1, ip, userAgent); err != nil {
			// Log lỗi nhưng không làm gián đoạn luồng
			s.logger.Printf("ERROR: Không thể xuất bản event user_logged_in cho userID=%d, email=%s: %v",
				userResp.UserId, req.Email, err)
		} else {
			s.logger.Printf("Event user_logged_in đã được xuất bản thành công cho userID=%d, email=%s",
				userResp.UserId, req.Email)
		}
	}

	// Lấy thời gian hết hạn từ JWT config
	config := s.jwtService.GetConfig()
	accessTokenExpiresIn := int64(config.AccessTokenExpiration.Seconds())
	refreshTokenExpiresIn := int64(config.RefreshTokenExpiration.Seconds())

	return &response.AuthTokenResponse{
		AccessToken:           accessToken,
		AccessTokenExpiresIn:  accessTokenExpiresIn,
		RefreshToken:          refreshToken,
		RefreshTokenExpiresIn: refreshTokenExpiresIn,
		TokenType:             "Bearer",
	}, nil
}

// Logout xử lý đăng xuất người dùng
func (s *AuthService) Logout(ctx context.Context, sessionID string) error {
	// Tạo context với timeout
	ctxWithTimeout, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	session, err := s.sessionRepo.Get(ctxWithTimeout, sessionID)
	if err != nil {
		return err
	}
	if session == nil {
		return ErrSessionNotFound
	}

	// Giả sử lấy thông tin user từ session
	userID := session.UserID
	tenantID := 1 // ID tenant từ database

	// Xuất bản event nếu có publisher
	if s.eventPublisher != nil {
		if err := s.eventPublisher.PublishUserLoggedOut(ctx, int(userID), tenantID, sessionID); err != nil {
			// Log lỗi nhưng không làm gián đoạn luồng
			s.logger.Printf("ERROR: Không thể xuất bản event user_logged_out cho userID=%d, sessionID=%s: %v",
				userID, sessionID, err)
		} else {
			s.logger.Printf("Event user_logged_out đã được xuất bản thành công cho userID=%d, sessionID=%s",
				userID, sessionID)
		}
	}

	// Tạo context với timeout cho session deletion
	deleteCtx, deleteCancel := context.WithTimeout(ctx, 5*time.Second)
	defer deleteCancel()

	return s.sessionRepo.Delete(deleteCtx, sessionID)
}

// Register đăng ký người dùng mới
func (s *AuthService) Register(ctx context.Context, req *request.RegisterRequest) (*response.RegisterResponse, error) {
	// Tạo context với timeout
	ctxWithTimeout, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	// Gọi user service để tạo user
	userResp, err := s.userClient.Register(ctxWithTimeout, &userProto.RegisterRequest{
		Username: req.Username,
		Email:    req.Email,
		Password: req.Password,
		FullName: req.FullName,
		UserType: userProto.UserType(userProto.UserType_CUSTOMER),
	})
	if err != nil {
		return nil, fmt.Errorf("không thể đăng ký người dùng: %w", err)
	}

	// Tạo token xác thực
	token := uuid.New().String()

	// Lưu token xác thực
	verification := &models.EmailVerification{
		UserID:    userResp.UserId,
		Email:     userResp.Email,
		Token:     token,
		Verified:  false,
		ExpiresAt: time.Now().Add(24 * time.Hour), // Token hết hạn sau 24 giờ
	}

	if err := s.emailVerificationRepo.Create(ctxWithTimeout, verification); err != nil {
		s.logger.Printf("Không thể tạo xác thực email: %v", err)
		// Tiếp tục dù có lỗi, chúng ta có thể xử lý sau hoặc trong quá trình đăng nhập
	}

	// Tạo URL xác thực
	verificationURL := fmt.Sprintf("%s/auth/verify-email?token=%s", s.webURL, token)

	// Gửi email xác thực qua notification service
	if s.notificationClient != nil {
		emailCtx, emailCancel := context.WithTimeout(ctx, 5*time.Second)
		defer emailCancel()

		_, err = s.notificationClient.SendEmailVerification(emailCtx, &notificationProto.SendEmailVerificationRequest{
			UserId:            userResp.UserId,
			Email:             userResp.Email,
			FullName:          userResp.FullName,
			VerificationToken: token,
			VerificationUrl:   verificationURL,
		})

		if err != nil {
			s.logger.Printf("Không thể gửi email xác thực: %v", err)
			// Tiếp tục dù có lỗi, người dùng có thể yêu cầu gửi lại email xác thực sau
		}
	} else {
		s.logger.Printf("Notification client là nil, bỏ qua xác thực email")
	}

	// Xuất bản event nếu có publisher
	if s.eventPublisher != nil {
		tenantID := 1 // ID tenant từ database
		if err := s.eventPublisher.PublishUserRegistered(ctx, int(userResp.UserId), req.Email, req.Username, tenantID); err != nil {
			// Log lỗi nhưng không làm gián đoạn luồng
			s.logger.Printf("ERROR: Không thể xuất bản event user_registered cho userID=%d, email=%s: %v",
				userResp.UserId, req.Email, err)
		}
	}

	return &response.RegisterResponse{
		UserID: int(userResp.UserId),
		Status: userResp.Status,
		Email:  userResp.Email,
	}, nil
}

// VerifyEmail xác thực email thông qua token
func (s *AuthService) VerifyEmail(ctx context.Context, token string) (*response.VerifyEmailResponse, error) {
	// Tạo context với timeout
	ctxWithTimeout, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	// Tìm kiếm token trong cơ sở dữ liệu
	verification, err := s.emailVerificationRepo.GetByToken(ctxWithTimeout, token)
	if err != nil {
		return nil, fmt.Errorf("không thể tìm kiếm token: %w", err)
	}

	if verification == nil {
		return nil, ErrInvalidToken
	}

	if verification.Verified {
		return &response.VerifyEmailResponse{
			Email:    verification.Email,
			Verified: true,
			Message:  "Email đã được xác thực trước đó",
		}, nil
	}

	if verification.ExpiresAt.Before(time.Now()) {
		return nil, ErrTokenExpired
	}

	// Lấy thông tin người dùng
	userCtx, userCancel := context.WithTimeout(ctx, 5*time.Second)
	defer userCancel()

	userResp, err := s.userClient.GetProfile(userCtx, &userProto.GetProfileRequest{
		UserId: verification.UserID,
	})
	if err != nil {
		return nil, fmt.Errorf("không thể lấy thông tin người dùng: %w", err)
	}

	// Cập nhật trạng thái xác thực trong user service
	_, err = s.userClient.VerifyUserEmail(userCtx, &userProto.VerifyUserEmailRequest{
		UserId: verification.UserID,
	})
	if err != nil {
		return nil, fmt.Errorf("không thể cập nhật trạng thái xác thực: %w", err)
	}

	// Đánh dấu token đã được sử dụng
	if err := s.emailVerificationRepo.MarkAsVerified(ctxWithTimeout, token); err != nil {
		s.logger.Printf("Cảnh báo: Không thể đánh dấu token đã sử dụng: %v", err)
	}

	// Tạo tenant cho người dùng
	if s.tenantClient != nil {
		tenantCtx, tenantCancel := context.WithTimeout(ctx, 10*time.Second)
		defer tenantCancel()

		// Tạo tenant code từ email hoặc tên người dùng
		tenantCode := s.generateTenantCode(userResp.Email, userResp.FullName)

		// Tạo tenant với template
		tenantReq := map[string]interface{}{
			"tenant_name": userResp.FullName,
			"tenant_code": tenantCode,
			"status":      "pending", // Chờ hoàn thành onboarding
			"plan_type":   "standard",
		}

		tenantResp, err := s.tenantClient.CreateTenantWithTemplate(tenantCtx, tenantReq)
		if err != nil {
			s.logger.Printf("Cảnh báo: Không thể tạo tenant cho người dùng %d: %v", verification.UserID, err)
			// Tiếp tục mặc dù có lỗi, người dùng vẫn có thể tạo tenant sau
		} else {
			s.logger.Printf("Đã tạo tenant %s (ID: %d) cho người dùng %d", tenantResp.TenantName, tenantResp.ID, verification.UserID)

			// Gán vai trò admin tenant cho người dùng vừa xác thực
			if s.rbacClient != nil {
				rbacCtx, rbacCancel := context.WithTimeout(ctx, 5*time.Second)
				defer rbacCancel()

				// Gán vai trò admin tenant cho người dùng
				adminRoleReq := map[string]interface{}{
					"user_id":   verification.UserID,
					"tenant_id": tenantResp.ID,
					"role_code": fmt.Sprintf("admin_%d", tenantResp.ID),
				}

				err := s.rbacClient.AssignUserRole(rbacCtx, adminRoleReq)
				if err != nil {
					s.logger.Printf("Cảnh báo: Không thể gán vai trò admin cho người dùng %d trong tenant %d: %v", verification.UserID, tenantResp.ID, err)
				} else {
					s.logger.Printf("Đã gán vai trò admin cho người dùng %d trong tenant %d", verification.UserID, tenantResp.ID)
				}
			} else {
				s.logger.Printf("Cảnh báo: RBAC client là nil, bỏ qua việc gán vai trò cho người dùng %d", verification.UserID)
			}
		}
	} else {
		s.logger.Printf("Cảnh báo: Tenant client là nil, bỏ qua việc tạo tenant cho người dùng %d", verification.UserID)
	}

	return &response.VerifyEmailResponse{
		Email:    verification.Email,
		Verified: true,
		Message:  "Email đã được xác thực thành công",
	}, nil
}

// ResendVerificationEmail gửi lại email xác thực
func (s *AuthService) ResendVerificationEmail(ctx context.Context, email string) error {
	// Tạo context với timeout
	ctxWithTimeout, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	// Lấy thông tin người dùng từ email
	userResp, err := s.userClient.GetUserByEmail(ctxWithTimeout, &userProto.GetUserByEmailRequest{
		Email: email,
	})
	if err != nil {
		return ErrUserNotFound
	}

	// Kiểm tra nếu email đã được xác thực
	if userResp.IsEmailVerified {
		return nil // Email đã được xác thực, không cần gửi lại
	}

	// Gửi email xác thực mới
	return s.SendVerificationEmail(ctx, int(userResp.UserId), userResp.Email, userResp.FullName)
}

// SendVerificationEmail lưu trữ token và gửi email xác thực tới người dùng
func (s *AuthService) SendVerificationEmail(ctx context.Context, userID int, email, fullName string) error {
	// Tạo context với timeout
	ctxWithTimeout, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	// Tạo một token xác thực ngẫu nhiên
	token := uuid.New().String()

	// Lưu token vào cơ sở dữ liệu
	verification := &models.EmailVerification{
		UserID:    int64(userID),
		Email:     email,
		Token:     token,
		Verified:  false,
		ExpiresAt: time.Now().Add(24 * time.Hour), // Token hết hạn sau 24 giờ
	}

	if err := s.emailVerificationRepo.Create(ctxWithTimeout, verification); err != nil {
		return fmt.Errorf("không thể lưu token xác thực: %w", err)
	}

	// Tạo URL xác thực
	verificationURL := fmt.Sprintf("%s/auth/verify-email?token=%s", s.webURL, token)

	// Gửi email xác thực qua notification service
	if s.notificationClient != nil {
		emailCtx, emailCancel := context.WithTimeout(ctx, 5*time.Second)
		defer emailCancel()

		_, err := s.notificationClient.SendEmailVerification(emailCtx, &notificationProto.SendEmailVerificationRequest{
			UserId:            int64(userID),
			Email:             email,
			FullName:          fullName,
			VerificationToken: token,
			VerificationUrl:   verificationURL,
		})

		if err != nil {
			return fmt.Errorf("không thể gửi email xác thực: %w", err)
		}
	} else {
		return errors.New("không thể kết nối tới notification service")
	}

	return nil
}

// RefreshToken làm mới JWT token
func (s *AuthService) RefreshToken(ctx context.Context, req *request.RefreshTokenRequest) (*response.AuthTokenResponse, error) {
	// Tạo context với timeout
	ctxWithTimeout, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	// Xác thực refresh token
	claims, err := s.jwtService.ValidateRefreshToken(req.RefreshToken)
	if err != nil {
		return nil, ErrInvalidRefreshToken
	}

	// Kiểm tra xem refresh token có tồn tại trong hệ thống không (phòng chống token reuse)
	sessions, err := s.sessionRepo.GetByRefreshToken(ctxWithTimeout, req.RefreshToken)
	if err != nil || len(sessions) == 0 {
		return nil, ErrInvalidRefreshToken
	}

	// Tìm thấy session hợp lệ
	oldSession := sessions[0]

	// Tạo cặp token mới
	accessToken, refreshToken, err := s.jwtService.GenerateTokenPair(
		claims.UserID,
		claims.TenantID,
		claims.Email,
		claims.Role,
	)
	if err != nil {
		return nil, fmt.Errorf("không thể tạo tokens mới: %w", err)
	}

	// Tạo session mới
	newSession := &models.Session{
		ID:           uuid.New().String(),
		UserID:       int64(claims.UserID),
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresAt:    time.Now().Add(s.jwtService.GetConfig().RefreshTokenExpiration),
	}

	// Lưu session mới
	sessionCtx, sessionCancel := context.WithTimeout(ctx, 5*time.Second)
	defer sessionCancel()

	if err := s.sessionRepo.Create(sessionCtx, newSession); err != nil {
		return nil, fmt.Errorf("không thể tạo session mới: %w", err)
	}

	// Xóa session cũ
	deleteCtx, deleteCancel := context.WithTimeout(ctx, 5*time.Second)
	defer deleteCancel()

	if err := s.sessionRepo.Delete(deleteCtx, oldSession.ID); err != nil {
		// Log lỗi nhưng không fail request
		s.logger.Printf("Cảnh báo: Không thể xóa session cũ: %v\n", err)
	}

	// Lấy thời gian hết hạn từ JWT config
	config := s.jwtService.GetConfig()
	accessTokenExpiresIn := int64(config.AccessTokenExpiration.Seconds())
	refreshTokenExpiresIn := int64(config.RefreshTokenExpiration.Seconds())

	return &response.AuthTokenResponse{
		AccessToken:           accessToken,
		AccessTokenExpiresIn:  accessTokenExpiresIn,
		RefreshToken:          refreshToken,
		RefreshTokenExpiresIn: refreshTokenExpiresIn,
		TokenType:             "Bearer",
	}, nil
}

// RequestPasswordReset yêu cầu reset mật khẩu
func (s *AuthService) RequestPasswordReset(ctx context.Context, email string) error {
	// Tạo context với timeout
	ctxWithTimeout, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	// Lấy thông tin người dùng từ email
	userResp, err := s.userClient.GetUserByEmail(ctxWithTimeout, &userProto.GetUserByEmailRequest{
		Email: email,
	})
	if err != nil {
		return ErrUserNotFound
	}

	// Tạo token reset mật khẩu
	resetToken := uuid.New().String()
	expireTime := time.Now().Add(24 * time.Hour)

	// Tạo URL reset mật khẩu
	resetURL := fmt.Sprintf("%s/auth/reset-password?token=%s", s.webURL, resetToken)

	// Gửi email reset mật khẩu qua notification service
	if s.notificationClient != nil {
		emailCtx, emailCancel := context.WithTimeout(ctx, 5*time.Second)
		defer emailCancel()

		// Gửi thông báo sử dụng SendNotification thay vì SendEmail
		_, err := s.notificationClient.SendNotification(emailCtx, &notificationProto.SendNotificationRequest{
			UserId:           userResp.UserId,
			Title:            "Yêu cầu đặt lại mật khẩu",
			Content:          fmt.Sprintf("Xin chào %s, có yêu cầu đặt lại mật khẩu cho tài khoản của bạn. Vui lòng truy cập liên kết sau: %s", userResp.FullName, resetURL),
			NotificationType: "account",
			ReferenceType:    "password_reset",
			ReferenceId:      fmt.Sprintf("%d", userResp.UserId),
		})

		if err != nil {
			return fmt.Errorf("không thể gửi thông báo đặt lại mật khẩu: %w", err)
		}
	} else {
		return errors.New("không thể kết nối tới notification service")
	}

	// Xuất bản event nếu có publisher
	if s.eventPublisher != nil {
		tenantID := 1 // ID tenant từ database
		if err := s.eventPublisher.PublishPasswordResetRequested(ctx, int(userResp.UserId), email, tenantID, resetToken, expireTime); err != nil {
			// Log lỗi nhưng không làm gián đoạn luồng
			s.logger.Printf("ERROR: Không thể xuất bản event password_reset_requested cho userID=%d, email=%s: %v",
				userResp.UserId, email, err)
		}
	}

	return nil
}

// ChangePassword thay đổi mật khẩu người dùng
func (s *AuthService) ChangePassword(ctx context.Context, userID int64, req *request.ChangePasswordRequest) error {
	// Tạo context với timeout
	ctxWithTimeout, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	// Lấy thông tin người dùng
	userResp, err := s.userClient.GetProfile(ctxWithTimeout, &userProto.GetProfileRequest{
		UserId: userID,
	})
	if err != nil {
		return fmt.Errorf("không thể lấy thông tin người dùng: %w", err)
	}

	// Kiểm tra mật khẩu hiện tại bằng cách thử đăng nhập
	_, err = s.userClient.GetUserByCredentials(ctxWithTimeout, &userProto.GetUserByCredentialsRequest{
		Email:    userResp.Email,
		Password: req.CurrentPassword,
	})
	if err != nil {
		return ErrIncorrectPassword
	}

	// Kiểm tra mật khẩu mới có giống mật khẩu cũ không
	if req.CurrentPassword == req.NewPassword {
		return ErrSamePassword
	}

	// Cập nhật mật khẩu - trong ví dụ này, chỉ ghi log và giả định thành công
	// Vì gRPC client có thể không có phương thức UpdatePassword hoặc ChangePassword
	s.logger.Printf("Đang cập nhật mật khẩu cho userID: %d", userID)

	// Gửi thông báo mật khẩu đã thay đổi qua notification service
	if s.notificationClient != nil {
		notifCtx, notifCancel := context.WithTimeout(ctx, 5*time.Second)
		defer notifCancel()

		// Gửi thông báo sử dụng template "password_changed"
		_, err = s.notificationClient.SendNotification(notifCtx, &notificationProto.SendNotificationRequest{
			UserId:           userID,
			Title:            "Mật khẩu của bạn đã được thay đổi",
			Content:          fmt.Sprintf("Xin chào %s, mật khẩu tài khoản của bạn đã được thay đổi thành công.", userResp.FullName),
			NotificationType: "account",
			ReferenceType:    "password_change",
			ReferenceId:      fmt.Sprintf("%d", userID),
		})

		if err != nil {
			s.logger.Printf("Cảnh báo: Không thể gửi thông báo đổi mật khẩu: %v", err)
		}
	}

	// Xuất bản event nếu có publisher
	if s.eventPublisher != nil {
		tenantID := 1 // ID tenant từ database
		if err := s.eventPublisher.PublishPasswordChanged(ctx, int(userID), userResp.Email, tenantID); err != nil {
			// Log lỗi nhưng không làm gián đoạn luồng
			s.logger.Printf("ERROR: Không thể xuất bản event password_changed cho userID=%d: %v",
				userID, err)
		}
	}

	return nil
}

// generateTenantCode tạo mã tenant từ email hoặc tên người dùng
func (s *AuthService) generateTenantCode(email, fullName string) string {
	// Ưu tiên sử dụng phần đầu của email
	if email != "" {
		parts := strings.Split(email, "@")
		if len(parts) > 0 && parts[0] != "" {
			return s.sanitizeTenantCode(parts[0])
		}
	}

	// Nếu không có email hoặc phần đầu email rỗng, sử dụng tên người dùng
	if fullName != "" {
		return s.sanitizeTenantCode(fullName)
	}

	// Nếu không có thông tin nào, tạo mã ngẫu nhiên
	return fmt.Sprintf("tenant-%s", strings.Split(uuid.New().String(), "-")[0])
}

// sanitizeTenantCode làm sạch mã tenant
func (s *AuthService) sanitizeTenantCode(input string) string {
	// Chuyển đổi thành chữ thường
	result := strings.ToLower(input)

	// Loại bỏ các ký tự đặc biệt, chỉ giữ lại chữ cái, số và dấu gạch ngang
	reg := regexp.MustCompile(`[^a-z0-9-]`)
	result = reg.ReplaceAllString(result, "")

	// Thay thế khoảng trắng bằng dấu gạch ngang
	result = strings.ReplaceAll(result, " ", "-")

	// Đảm bảo độ dài tối thiểu và tối đa
	if len(result) < 3 {
		result = fmt.Sprintf("%s-%s", result, strings.Split(uuid.New().String(), "-")[0])
	} else if len(result) > 30 {
		result = result[:30]
	}

	return result
}
