package request

import "github.com/webnew/wn-backend-v2/modules/user/models"

type CreateUserRequest struct {
	TenantID uint            `json:"tenant_id" binding:"required,min=1"`
	Username string          `json:"username" binding:"required,min=3,max=50"`
	Email    string          `json:"email" binding:"required,email"`
	Password string          `json:"password" binding:"required,min=8"`
	FullName string          `json:"full_name" binding:"required"`
	UserType models.UserType `json:"user_type" binding:"required"`
}
