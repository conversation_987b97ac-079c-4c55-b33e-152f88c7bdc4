package request

type UpdateUserRequest struct {
	Username *string `json:"username" binding:"omitempty,min=3,max=50"`
	Email    *string `json:"email" binding:"omitempty,email"`
	FullName *string `json:"full_name"`
	Status   *string `json:"status" binding:"omitempty,oneof=active inactive suspended banned locked"`
	TenantID *uint   `json:"tenant_id" binding:"omitempty,min=1"`
	UserType *string `json:"user_type" binding:"omitempty,oneof=admin tenant customer"`
}
