package response

import (
	"time"

	"github.com/webnew/wn-backend-v2/modules/user/models"
)

type UserResponse struct {
	UserID          uint            `json:"user_id"`
	TenantID        uint            `json:"tenant_id"`
	Username        string          `json:"username"`
	Email           string          `json:"email"`
	FullName        string          `json:"full_name"`
	Phone           string          `json:"phone,omitempty"`
	Role            string          `json:"role,omitempty"`
	Status          string          `json:"status"`
	LastLogin       *time.Time      `json:"last_login,omitempty"`
	CreatedAt       time.Time       `json:"created_at"`
	UpdatedAt       time.Time       `json:"updated_at"`
	IsEmailVerified bool            `json:"is_email_verified"`
	UserType        models.UserType `json:"user_type"`
}

func NewUserResponse(user *models.User) *UserResponse {
	if user == nil {
		return nil
	}

	return &UserResponse{
		UserID:          user.UserID,
		TenantID:        user.TenantID,
		Username:        user.Username,
		Email:           user.Email,
		FullName:        user.FullName,
		Status:          string(user.Status),
		LastLogin:       user.LastLogin,
		CreatedAt:       user.CreatedAt,
		UpdatedAt:       user.UpdatedAt,
		IsEmailVerified: user.IsEmailVerified,
		UserType:        user.UserType,
	}
}
