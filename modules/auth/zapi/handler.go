package api

import (
	"errors"
	"net/http"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/tracing"
	"wnapi/modules/auth/dto"
	"wnapi/modules/auth/internal"

	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/otel/attribute"
)

// Handler xử lý các HTTP request cho auth module
type Handler struct {
	service internal.AuthService
	logger  logger.Logger
}

// NewHandler tạo handler mới
func NewHandler(service internal.AuthService, log logger.Logger) *Handler {
	return &Handler{
		service: service,
		logger:  log,
	}
}

// Register xử lý request đăng ký
func (h *Handler) Register(c *gin.Context) {
	// Add tracing attributes for this operation
	tracing.AddGinSpanAttributes(c,
		tracing.Module("auth"),
		tracing.Function("register"),
		attribute.String("operation.type", "user_registration"),
	)

	var req dto.RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		tracing.RecordGinError(c, err, attribute.String("error.type", "validation"))
		c.J<PERSON>(http.StatusBadRequest, dto.APIResponse{
			Success: false,
			Message: "Invalid request format",
		})
		return
	}

	// Add user details to span (without sensitive data)
	tracing.AddGinSpanAttributes(c,
		attribute.String("user.username", req.Username),
		attribute.String("user.email", req.Email),
	)

	user, err := h.service.Register(c.Request.Context(), req)
	if err != nil {
		statusCode := http.StatusInternalServerError
		message := "Failed to register user"

		if errors.Is(err, internal.ErrUserAlreadyExists) {
			statusCode = http.StatusConflict
			message = "Username or email already exists"
			tracing.RecordGinError(c, err, attribute.String("error.type", "user_already_exists"))
		} else {
			tracing.RecordGinError(c, err, attribute.String("error.type", "registration_failed"))
		}

		c.JSON(statusCode, dto.APIResponse{
			Success: false,
			Message: message,
		})
		return
	}

	// Add success metrics
	tracing.AddGinSpanAttributes(c,
		attribute.Int("user.id", user.ID),
		attribute.String("result", "success"),
	)

	c.JSON(http.StatusCreated, dto.APIResponse{
		Success: true,
		Message: "Registration successful",
		Data: gin.H{
			"id":       user.ID,
			"username": user.Username,
		},
	})
}

// Login xử lý request đăng nhập
func (h *Handler) Login(c *gin.Context) {
	var req dto.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, dto.APIResponse{
			Success: false,
			Message: "Invalid request format",
		})
		return
	}

	response, err := h.service.Login(c.Request.Context(), req)
	if err != nil {
		statusCode := http.StatusInternalServerError
		message := "Failed to authenticate"

		if errors.Is(err, internal.ErrInvalidCredentials) {
			statusCode = http.StatusUnauthorized
			message = "Invalid username or password"
		}

		c.JSON(statusCode, dto.APIResponse{
			Success: false,
			Message: message,
		})
		return
	}

	c.JSON(http.StatusOK, dto.APIResponse{
		Success: true,
		Message: "Login successful",
		Data:    response,
	})
}

// RefreshToken xử lý request làm mới token
func (h *Handler) RefreshToken(c *gin.Context) {
	var req dto.RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, dto.APIResponse{
			Success: false,
			Message: "Invalid request format",
		})
		return
	}

	response, err := h.service.RefreshToken(c.Request.Context(), req.RefreshToken)
	if err != nil {
		statusCode := http.StatusInternalServerError
		message := "Failed to refresh token"

		switch {
		case errors.Is(err, internal.ErrInvalidToken):
			statusCode = http.StatusUnauthorized
			message = "Invalid refresh token"
		case errors.Is(err, internal.ErrExpiredToken):
			statusCode = http.StatusUnauthorized
			message = "Refresh token has expired"
		}

		c.JSON(statusCode, dto.APIResponse{
			Success: false,
			Message: message,
		})
		return
	}

	c.JSON(http.StatusOK, dto.APIResponse{
		Success: true,
		Message: "Token refreshed successfully",
		Data:    response,
	})
}
