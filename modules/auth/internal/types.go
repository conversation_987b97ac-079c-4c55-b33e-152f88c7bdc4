package internal

import (
	"context"
	"time"
	"wnapi/modules/auth/dto"
)

// AuthConfig chứa cấu hình auth service
type AuthConfig struct {
	JWTSecret          string        `yaml:"jwt_secret" env:"JWT_SECRET"`
	AccessTokenExpiry  time.Duration `yaml:"access_token_expiry" env:"ACCESS_TOKEN_EXPIRY" envDefault:"15m"`
	RefreshTokenExpiry time.Duration `yaml:"refresh_token_expiry" env:"REFRESH_TOKEN_EXPIRY" envDefault:"168h"`
	Message            string        `env:"MESSAGE" envDefault:"Xin chào từ module Auth!"`
}

// ServiceError định nghĩa các lỗi service
type ServiceError string

const (
	// ErrInvalidCredentials là lỗi khi thông tin đăng nhập không hợp lệ
	ErrInvalidCredentials ServiceError = "invalid_credentials"
	// ErrUserAlreadyExists là lỗi khi user đã tồn tại
	ErrUserAlreadyExists ServiceError = "user_already_exists"
	// ErrInvalidToken là lỗi khi token không hợp lệ
	ErrInvalidToken ServiceError = "invalid_token"
	// ErrExpiredToken là lỗi khi token đã hết hạn
	ErrExpiredToken ServiceError = "expired_token"
)

func (e ServiceError) Error() string {
	return string(e)
}

// AuthService định nghĩa interface cho auth service
type AuthService interface {
	Register(ctx context.Context, req dto.RegisterRequest) (*UserInfo, error)
	Login(ctx context.Context, req dto.LoginRequest) (*dto.LoginResponse, error)
	RefreshToken(ctx context.Context, refreshToken string) (*dto.LoginResponse, error)
	ValidateToken(tokenString string) (map[string]interface{}, error)
}

// UserInfo chứa thông tin người dùng đã đăng ký
type UserInfo struct {
	ID       int    `json:"id"`
	Username string `json:"username"`
	Email    string `json:"email"`
}

// Repository định nghĩa interface cho authentication repository
// Được chuyển từ repository/repository.go để tránh import cycle

type Repository interface {
	// User
	CreateUser(ctx context.Context, user *User, password string) error
	GetUserByID(ctx context.Context, id int) (*User, error)
	GetUserByUsername(ctx context.Context, username string) (*User, error)
	GetUserByEmail(ctx context.Context, email string) (*User, error)
	UpdateUser(ctx context.Context, user *User) error
	DeleteUser(ctx context.Context, id int) error

	// Token
	CreateToken(ctx context.Context, token *Token) error
	GetTokenByValue(ctx context.Context, tokenValue string, tokenType TokenType) (*Token, error)
	DeleteToken(ctx context.Context, id string) error
	DeleteExpiredTokens(ctx context.Context) error
}

var (
	ErrUserNotFound    = ServiceError("user_not_found")
	ErrTokenNotFound   = ServiceError("token_not_found")
	ErrDuplicateUser   = ServiceError("user_already_exists")
	ErrInvalidPassword = ServiceError("invalid_password")
)

// User đại diện cho người dùng trong hệ thống
type User struct {
	ID           int       `db:"id" json:"id"`
	Username     string    `db:"username" json:"username"`
	Email        string    `db:"email" json:"email"`
	PasswordHash string    `db:"password_hash" json:"-"`
	FullName     string    `db:"full_name" json:"full_name"`
	IsActive     bool      `db:"is_active" json:"is_active"`
	CreatedAt    time.Time `db:"created_at" json:"created_at"`
	UpdatedAt    time.Time `db:"updated_at" json:"updated_at"`
}

// TokenType định nghĩa loại token
type TokenType string

const (
	// TokenTypeAccess là token dùng để xác thực API
	TokenTypeAccess TokenType = "access"
	// TokenTypeRefresh là token dùng để refresh access token
	TokenTypeRefresh TokenType = "refresh"
	// TokenTypePasswordReset là token dùng để reset password
	TokenTypePasswordReset TokenType = "password_reset"
)

// Token đại diện cho token xác thực
type Token struct {
	ID        string    `db:"id" json:"id"`
	UserID    int       `db:"user_id" json:"user_id"`
	TokenType TokenType `db:"token_type" json:"token_type"`
	Value     string    `db:"token_value" json:"token"`
	ExpiresAt time.Time `db:"expires_at" json:"expires_at"`
	CreatedAt time.Time `db:"created_at" json:"created_at"`
}

// IsExpired kiểm tra token có hết hạn chưa
func (t *Token) IsExpired() bool {
	return time.Now().After(t.ExpiresAt)
}
