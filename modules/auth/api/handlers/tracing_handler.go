// This file is no longer needed as we're using the middleware approach instead
// We'll keep it for reference but it won't be used

package handlers

import (
	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/trace"
)

// TracingHandler wraps handler methods with tracing
type Tracing<PERSON>andler struct {
	tracer trace.Tracer
}

// NewTracingHandler creates a new tracing handler
func NewTracingHandler(tracer trace.Tracer) *TracingHandler {
	return &TracingHandler{
		tracer: tracer,
	}
}

// TraceHandler wraps a handler function with tracing
func (h *TracingHandler) TraceHandler(handlerName string, handler gin.HandlerFunc) gin.HandlerFunc {
	return func(c *gin.Context) {
		if h.tracer == nil {
			handler(c)
			return
		}

		// Start a new span
		ctx, span := h.tracer.Start(c.Request.Context(), handlerName,
			trace.WithAttributes(
				attribute.String("handler", handlerName),
				attribute.String("method", c.Request.Method),
				attribute.String("path", c.<PERSON>()),
			),
		)
		defer span.End()

		// Update the context
		c.Request = c.Request.WithContext(ctx)

		// Call the handler
		handler(c)
	}
}

// WrapAuthHandler wraps an AuthHandler with tracing
// Note: This function is not used in the current implementation
func WrapAuthHandler(handler *AdminAuthHandler, tracer trace.Tracer) *AdminAuthHandler {
	// In the current implementation, AuthHandler doesn't have fields for each handler method
	// Instead, it has methods that implement the handler functions
	// So we can't wrap the handler methods this way
	return handler
}

// WrapPasswordResetHandler wraps a PasswordResetHandler with tracing
// Note: This function is not used in the current implementation
func WrapPasswordResetHandler(handler *AdminPasswordResetHandler, tracer trace.Tracer) *AdminPasswordResetHandler {
	// In the current implementation, PasswordResetHandler doesn't have fields for each handler method
	// Instead, it has methods that implement the handler functions
	// So we can't wrap the handler methods this way
	return handler
}

// WrapEmailVerificationHandler wraps an EmailVerificationHandler with tracing
// Note: This function is not used in the current implementation
func WrapEmailVerificationHandler(handler *AdminEmailVerificationHandler, tracer trace.Tracer) *AdminEmailVerificationHandler {
	// In the current implementation, EmailVerificationHandler doesn't have fields for each handler method
	// Instead, it has methods that implement the handler functions
	// So we can't wrap the handler methods this way
	return handler
}
