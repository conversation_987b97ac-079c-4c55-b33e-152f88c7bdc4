package handlers

import (
	"net/http"

	"github.com/webnew/wn-backend-v2/modules/auth/dto/request"
	"github.com/webnew/wn-backend-v2/modules/auth/service"
	"github.com/webnew/wn-backend-v2/pkg/response"

	"github.com/gin-gonic/gin"
)

// AdminEmailVerificationHandler xử lý các yêu cầu xác thực email cho admin
type AdminEmailVerificationHandler struct {
	authService *service.AuthService
}

// NewAdminEmailVerificationHandler tạo một instance mới của AdminEmailVerificationHandler
func NewAdminEmailVerificationHandler(authService *service.AuthService) *AdminEmailVerificationHandler {
	return &AdminEmailVerificationHandler{
		authService: authService,
	}
}

// VerifyEmail xử lý việc xác thực email thông qua token
func (h *AdminEmailVerificationHandler) VerifyEmail(c *gin.Context) {
	var req request.VerifyEmailRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Định dạng yêu cầu không hợp lệ", "INVALID_REQUEST", details)
		return
	}

	resp, err := h.authService.VerifyEmail(c.Request.Context(), req.Token)
	if err != nil {
		if err.Error() == "token không hợp lệ" || err.Error() == "token đã hết hạn" {
			response.Error(c, http.StatusNotFound, "Mã xác thực không hợp lệ hoặc đã hết hạn", err)
		} else {
			response.Error(c, http.StatusInternalServerError, "Không thể xác thực email", err)
		}
		return
	}

	response.Success(c, http.StatusOK, "Email đã được xác thực thành công", resp)
}

// ResendVerification xử lý việc gửi lại email xác thực
func (h *AdminEmailVerificationHandler) ResendVerification(c *gin.Context) {
	var req request.ResendVerificationEmailRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Định dạng yêu cầu không hợp lệ", "INVALID_REQUEST", details)
		return
	}

	err := h.authService.ResendVerificationEmail(c.Request.Context(), req.Email)
	if err != nil {
		if err.Error() == "không tìm thấy người dùng với email này" {
			response.Error(c, http.StatusNotFound, "Không tìm thấy người dùng với email này", err)
		} else {
			response.Error(c, http.StatusInternalServerError, "Không thể gửi lại email xác thực", err)
		}
		return
	}

	response.Success(c, http.StatusOK, "Email xác thực đã được gửi lại", nil)
}
