package handlers

import (
	"net/http"

	"github.com/webnew/wn-backend-v2/modules/auth/dto/request"
	"github.com/webnew/wn-backend-v2/modules/auth/service"
	"github.com/webnew/wn-backend-v2/pkg/response"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
)

type AdminAuthHandler struct {
	authService *service.AuthService
}

func NewAdminAuthHandler(authService *service.AuthService) *AdminAuthHandler {
	return &AdminAuthHandler{authService: authService}
}

func (h *AdminAuthHandler) Login(c *gin.Context) {
	var req request.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Định dạng yêu cầu không hợp lệ", "INVALID_REQUEST", details)
		return
	}

	// Thêm tham số để chỉ định rằng đây là đăng nhập admin
	// và chỉ chấp nhận user_type là admin hoặc tenant
	req.AdminLogin = true

	resp, err := h.authService.Login(c.Request.Context(), &req)
	if err != nil {
		switch err {
		case service.ErrInvalidCredentials:
			details := []interface{}{map[string]string{"message": "Email hoặc mật khẩu không đúng"}}
			response.ErrorWithDetails(c, http.StatusUnauthorized, "Thông tin đăng nhập không hợp lệ", "INVALID_CREDENTIALS", details)
		case service.ErrEmailNotVerified:
			details := []interface{}{map[string]string{"message": "Vui lòng xác thực email của bạn trước khi đăng nhập"}}
			response.ErrorWithDetails(c, http.StatusForbidden, "Email chưa được xác thực", "EMAIL_NOT_VERIFIED", details)
		case service.ErrInvalidUserType:
			details := []interface{}{map[string]string{"message": "Tài khoản của bạn không có quyền truy cập vào hệ thống quản trị"}}
			response.ErrorWithDetails(c, http.StatusForbidden, "Không có quyền truy cập", "ACCESS_DENIED", details)
		default:
			details := []interface{}{map[string]string{"message": err.Error()}}
			response.ErrorWithDetails(c, http.StatusInternalServerError, "Lỗi hệ thống", "INTERNAL_ERROR", details)
		}
		return
	}

	response.Success(c, http.StatusOK, "Đăng nhập thành công", resp)
}

func (h *AdminAuthHandler) Logout(c *gin.Context) {
	sessionID := c.GetHeader("X-Session-ID")
	if sessionID == "" {
		details := []interface{}{map[string]string{"message": "Session ID không được cung cấp"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Session ID is required", "MISSING_SESSION_ID", details)
		return
	}

	if err := h.authService.Logout(c.Request.Context(), sessionID); err != nil {
		if err == service.ErrSessionNotFound {
			details := []interface{}{map[string]string{"message": err.Error()}}
			response.ErrorWithDetails(c, http.StatusNotFound, "Session không tồn tại", "SESSION_NOT_FOUND", details)
			return
		}
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Internal server error", "INTERNAL_ERROR", details)
		return
	}

	response.Success(c, http.StatusOK, "Logout successful", nil)
}

func (h *AdminAuthHandler) Register(c *gin.Context) {
	var req request.RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		if validationErrors, ok := err.(validator.ValidationErrors); ok {
			details := make([]interface{}, len(validationErrors))
			for i, e := range validationErrors {
				fieldName := toSnakeCase(e.Field())
				details[i] = map[string]string{
					"field":   fieldName,
					"message": getValidationErrorMessage(e),
				}
			}
			response.ErrorWithDetails(c, http.StatusBadRequest, "Dữ liệu không hợp lệ", "VALIDATION_ERROR", details)
			return
		}
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Định dạng yêu cầu không hợp lệ", "INVALID_REQUEST", details)
		return
	}

	// Always set user_type to 'tenant' for admin registration
	req.UserType = "tenant"

	resp, err := h.authService.Register(c.Request.Context(), &req)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Không thể đăng ký tài khoản", "REGISTRATION_FAILED", details)
		return
	}

	response.Success(c, http.StatusCreated, "Đăng ký thành công. Vui lòng kiểm tra email để xác thực tài khoản.", resp)
}

func (h *AdminAuthHandler) RefreshToken(c *gin.Context) {
	var req request.RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request format", "INVALID_REQUEST", details)
		return
	}

	resp, err := h.authService.RefreshToken(c.Request.Context(), &req)
	if err != nil {
		if err == service.ErrInvalidRefreshToken {
			details := []interface{}{map[string]string{"message": "Refresh token không hợp lệ hoặc đã hết hạn"}}
			response.ErrorWithDetails(c, http.StatusUnauthorized, "Invalid refresh token", "INVALID_REFRESH_TOKEN", details)
			return
		}
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Internal server error", "INTERNAL_ERROR", details)
		return
	}

	response.Success(c, http.StatusOK, "Token refreshed successfully", resp)
}

// ChangePassword xử lý yêu cầu đổi mật khẩu
func (h *AdminAuthHandler) ChangePassword(c *gin.Context) {
	// Xác thực JWT token để đảm bảo người dùng đã đăng nhập
	userID, exists := c.Get("userID")
	if !exists {
		details := []interface{}{map[string]string{"message": "Không xác định được người dùng"}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	var req request.ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		// Xử lý lỗi validation
		if validationErrors, ok := err.(validator.ValidationErrors); ok {
			details := make([]interface{}, len(validationErrors))
			for i, e := range validationErrors {
				fieldName := toSnakeCase(e.Field())
				details[i] = map[string]string{
					"field":   fieldName,
					"message": getValidationErrorMessage(e),
				}
			}
			response.ErrorWithDetails(c, http.StatusBadRequest, "Dữ liệu không hợp lệ", "VALIDATION_ERROR", details)
			return
		}

		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Định dạng yêu cầu không hợp lệ", "INVALID_REQUEST", details)
		return
	}

	// Gọi service để thực hiện đổi mật khẩu
	err := h.authService.ChangePassword(c.Request.Context(), userID.(int64), &req)
	if err != nil {
		switch err {
		case service.ErrIncorrectPassword:
			response.Error(c, http.StatusUnauthorized, "Mật khẩu hiện tại không chính xác", err)
		case service.ErrSamePassword:
			details := []interface{}{map[string]string{"field": "new_password", "message": "Mật khẩu mới không được giống mật khẩu hiện tại"}}
			response.ErrorWithDetails(c, http.StatusBadRequest, "Mật khẩu mới không được giống mật khẩu hiện tại", "SAME_PASSWORD", details)
		default:
			response.Error(c, http.StatusInternalServerError, "Không thể đổi mật khẩu", err)
		}
		return
	}

	// Trả về thành công
	response.Success(c, http.StatusOK, "Đổi mật khẩu thành công", nil)
}

func getValidationErrorMessage(e validator.FieldError) string {
	switch e.Tag() {
	case "required":
		return "This field is required"
	case "email":
		return "Invalid email format"
	case "min":
		return "Value is too short"
	case "max":
		return "Value is too long"
	case "eqfield":
		return "Fields do not match"
	case "containsany":
		switch e.Param() {
		case "ABCDEFGHIJKLMNOPQRSTUVWXYZ":
			return "Must contain at least one uppercase letter"
		case "abcdefghijklmnopqrstuvwxyz":
			return "Must contain at least one lowercase letter"
		case "0123456789":
			return "Must contain at least one number"
		case "!@#$%^&*()_+-=[]{}|;':\",./<>?":
			return "Must contain at least one special character"
		}
		return "Invalid value"
	default:
		return "Invalid value"
	}
}

func toSnakeCase(input string) string {
	if input == "Username" {
		return "username"
	} else if input == "Email" {
		return "email"
	} else if input == "Password" {
		return "password"
	} else if input == "FullName" {
		return "full_name"
	}
	return input
}
