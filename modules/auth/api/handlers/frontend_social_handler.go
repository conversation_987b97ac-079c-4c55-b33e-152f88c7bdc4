package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/webnew/wn-backend-v2/modules/auth/dto"
	"github.com/webnew/wn-backend-v2/modules/auth/service"
	"github.com/webnew/wn-backend-v2/pkg/logging"
	"github.com/webnew/wn-backend-v2/pkg/response"
)

// FrontendSocialHandler handles social login/registration for frontend users
type FrontendSocialHandler struct {
	socialAuthService service.SocialAuthService
	logger            logging.Logger
}

// NewFrontendSocialHandler creates a new instance of FrontendSocialHandler
func NewFrontendSocialHandler(
	socialAuthService service.SocialAuthService,
	logger logging.Logger,
) *FrontendSocialHandler {
	return &FrontendSocialHandler{
		socialAuthService: socialAuthService,
		logger:            logger.WithField("component", "FrontendSocialHandler"),
	}
}

// GenerateFacebookLoginURL generates the Facebook OAuth URL for frontend users
func (h *FrontendSocialHandler) GenerateFacebookLoginURL(c *gin.Context) {
	redirectURI := c.Query("redirect_uri")
	if redirectURI == "" {
		response.Error(c, http.StatusBadRequest, "Missing redirect_uri parameter", nil)
		return
	}

	url, err := h.socialAuthService.GenerateFacebookAuthURL(c.Request.Context(), redirectURI, "frontend")
	if err != nil {
		h.logger.Error("Failed to generate Facebook login URL", err)
		response.Error(c, http.StatusInternalServerError, "Failed to generate Facebook login URL", err)
		return
	}

	response.Success(c, http.StatusOK, "Facebook login URL generated successfully", gin.H{"url": url})
}

// HandleFacebookCallback handles the OAuth callback from Facebook for frontend users
func (h *FrontendSocialHandler) HandleFacebookCallback(c *gin.Context) {
	code := c.Query("code")
	if code == "" {
		errorMsg := c.Query("error_description")
		if errorMsg == "" {
			errorMsg = "Authorization code not provided"
		}
		response.Error(c, http.StatusBadRequest, errorMsg, nil)
		return
	}

	// Get state which contains the original redirect_uri
	state := c.Query("state")

	authResponse, err := h.socialAuthService.ProcessFacebookCallback(c.Request.Context(), code, state, "frontend")
	if err != nil {
		h.logger.Error("Failed to process Facebook callback", err)
		response.Error(c, http.StatusInternalServerError, "Failed to authenticate with Facebook", err)
		return
	}

	response.Success(c, http.StatusOK, "Facebook authentication successful", authResponse)
}

// GenerateGoogleLoginURL generates the Google OAuth URL for frontend users
func (h *FrontendSocialHandler) GenerateGoogleLoginURL(c *gin.Context) {
	redirectURI := c.Query("redirect_uri")
	if redirectURI == "" {
		response.Error(c, http.StatusBadRequest, "Missing redirect_uri parameter", nil)
		return
	}

	url, err := h.socialAuthService.GenerateGoogleAuthURL(c.Request.Context(), redirectURI, "frontend")
	if err != nil {
		h.logger.Error("Failed to generate Google login URL", err)
		response.Error(c, http.StatusInternalServerError, "Failed to generate Google login URL", err)
		return
	}

	response.Success(c, http.StatusOK, "Google login URL generated successfully", gin.H{"url": url})
}

// HandleGoogleCallback handles the OAuth callback from Google for frontend users
func (h *FrontendSocialHandler) HandleGoogleCallback(c *gin.Context) {
	code := c.Query("code")
	if code == "" {
		errorMsg := c.Query("error_description")
		if errorMsg == "" {
			errorMsg = "Authorization code not provided"
		}
		response.Error(c, http.StatusBadRequest, errorMsg, nil)
		return
	}

	// Get state which contains the original redirect_uri
	state := c.Query("state")

	authResponse, err := h.socialAuthService.ProcessGoogleCallback(c.Request.Context(), code, state, "frontend")
	if err != nil {
		h.logger.Error("Failed to process Google callback", err)
		response.Error(c, http.StatusInternalServerError, "Failed to authenticate with Google", err)
		return
	}

	response.Success(c, http.StatusOK, "Google authentication successful", authResponse)
}

// HandleSocialLogin processes a social login token directly from the client
// This is useful for mobile apps and SPAs that handle the OAuth flow on the client side
func (h *FrontendSocialHandler) HandleSocialLogin(c *gin.Context) {
	var req dto.SocialLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "Invalid request data", err)
		return
	}

	if req.Provider != "facebook" && req.Provider != "google" {
		response.Error(c, http.StatusBadRequest, "Unsupported provider", nil)
		return
	}

	if req.Token == "" {
		response.Error(c, http.StatusBadRequest, "Token is required", nil)
		return
	}

	authResponse, err := h.socialAuthService.ProcessSocialToken(c.Request.Context(), req, "frontend")
	if err != nil {
		h.logger.Error("Failed to process social token", err)
		response.Error(c, http.StatusUnauthorized, "Failed to authenticate with social provider", err)
		return
	}

	response.Success(c, http.StatusOK, "Social authentication successful", authResponse)
}

// LinkSocialAccount links a social account to a logged-in user's account
func (h *FrontendSocialHandler) LinkSocialAccount(c *gin.Context) {
	var req dto.SocialLinkRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "Invalid request data", err)
		return
	}

	// Get user ID from context (assuming it was set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		response.Error(c, http.StatusUnauthorized, "User not authenticated", nil)
		return
	}

	err := h.socialAuthService.LinkSocialAccount(c.Request.Context(), req, userID.(int64))
	if err != nil {
		h.logger.Error("Failed to link social account", err)
		response.Error(c, http.StatusInternalServerError, "Failed to link social account", err)
		return
	}

	response.Success(c, http.StatusOK, "Social account linked successfully", nil)
}

// UnlinkSocialAccount removes a link between a social account and a user's account
func (h *FrontendSocialHandler) UnlinkSocialAccount(c *gin.Context) {
	provider := c.Param("provider")
	if provider != "facebook" && provider != "google" {
		response.Error(c, http.StatusBadRequest, "Unsupported provider", nil)
		return
	}

	// Get user ID from context (assuming it was set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		response.Error(c, http.StatusUnauthorized, "User not authenticated", nil)
		return
	}

	err := h.socialAuthService.UnlinkSocialAccount(c.Request.Context(), provider, userID.(int64))
	if err != nil {
		h.logger.Error("Failed to unlink social account", err)
		response.Error(c, http.StatusInternalServerError, "Failed to unlink social account", err)
		return
	}

	response.Success(c, http.StatusOK, "Social account unlinked successfully", nil)
}

// GetLinkedSocialAccounts retrieves all social accounts linked to the user's account
func (h *FrontendSocialHandler) GetLinkedSocialAccounts(c *gin.Context) {
	// Get user ID from context (assuming it was set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		response.Error(c, http.StatusUnauthorized, "User not authenticated", nil)
		return
	}

	accounts, err := h.socialAuthService.GetLinkedSocialAccounts(c.Request.Context(), userID.(int64))
	if err != nil {
		h.logger.Error("Failed to get linked social accounts", err)
		response.Error(c, http.StatusInternalServerError, "Failed to get linked social accounts", err)
		return
	}

	response.Success(c, http.StatusOK, "Social accounts retrieved successfully", accounts)
}
