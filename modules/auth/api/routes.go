package api

import (
	"github.com/gin-gonic/gin"
	"github.com/webnew/wn-backend-v2/modules/auth/api/handlers"
	"github.com/webnew/wn-backend-v2/pkg/auth"
)

// RegisterRoutes đã bị loại bỏ
// Sử dụng RegisterFrontendRoutes hoặc RegisterAdminRoutes thay thế

// RegisterFrontendRoutes đăng ký các routes cho frontend API
func RegisterFrontendRoutes(
	router *gin.RouterGroup,
	authHandler *handlers.FrontendAuthHandler,
	passwordResetHandler *handlers.FrontendPasswordResetHandler,
	emailVerificationHandler *handlers.FrontendEmailVerificationHandler,
	jwtService *auth.JWTService,
) {
	// ===== FRONTEND ROUTES =====
	// Không yêu cầu xác thực - Frontend
	router.POST("/login", authHandler.Login)
	router.POST("/signin", authHandler.Login) // Alias for login
	router.POST("/register", authHandler.Register)
	router.POST("/signup", authHandler.Register) // Alias for register
	router.POST("/refresh-token", authHandler.RefreshToken)
	router.POST("/token/refresh", authHandler.RefreshToken) // Alias for refresh-token

	// Routes cho quên mật khẩu - Frontend
	router.POST("/forgot-password", passwordResetHandler.ForgotPassword)
	router.GET("/verify-reset-token", passwordResetHandler.VerifyResetToken)
	router.POST("/reset-password", passwordResetHandler.ResetPassword)

	// Routes cho xác thực email - Frontend
	router.GET("/verify-email", emailVerificationHandler.VerifyEmail)
	router.POST("/resend-verification", emailVerificationHandler.ResendVerification)

	// Yêu cầu xác thực - Frontend
	authenticated := router.Group("/")
	authenticated.Use(jwtService.JWTAuthMiddleware())
	{
		authenticated.POST("/logout", authHandler.Logout)
		authenticated.PUT("/change-password", authHandler.ChangePassword)
	}
}

// RegisterAdminRoutes đăng ký các routes cho admin API
func RegisterAdminRoutes(
	router *gin.RouterGroup,
	authHandler *handlers.AdminAuthHandler,
	passwordResetHandler *handlers.AdminPasswordResetHandler,
	emailVerificationHandler *handlers.AdminEmailVerificationHandler,
	jwtService *auth.JWTService,
) {
	// ===== ADMIN ROUTES =====
	// Không yêu cầu xác thực - Admin
	router.POST("/login", authHandler.Login)
	router.POST("/signin", authHandler.Login) // Alias for login
	router.POST("/register", authHandler.Register)
	router.POST("/signup", authHandler.Register) // Alias for register
	router.POST("/refresh-token", authHandler.RefreshToken)
	router.POST("/token/refresh", authHandler.RefreshToken) // Alias for refresh-token

	// Routes cho quên mật khẩu - Admin
	router.POST("/forgot-password", passwordResetHandler.ForgotPassword)
	router.GET("/verify-reset-token", passwordResetHandler.VerifyResetToken)
	router.POST("/reset-password", passwordResetHandler.ResetPassword)

	// Routes cho xác thực email - Admin
	router.GET("/verify-email", emailVerificationHandler.VerifyEmail)
	router.POST("/resend-verification", emailVerificationHandler.ResendVerification)

	// Yêu cầu xác thực - Admin
	authenticated := router.Group("/")
	authenticated.Use(jwtService.JWTAuthMiddleware())
	{
		authenticated.POST("/logout", authHandler.Logout)
		authenticated.PUT("/change-password", authHandler.ChangePassword)
	}
}
