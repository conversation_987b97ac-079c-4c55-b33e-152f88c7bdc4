package router

import (
	"github.com/gin-gonic/gin"

	"github.com/webnew/wn-backend-v2/modules/auth/api/handlers"
)

// <PERSON><PERSON><PERSON> nghĩa AuthMiddleware interface
type AuthMiddleware interface {
	RequireAuth() gin.HandlerFunc
}

// RegisterFrontendRoutes registers frontend API routes
func RegisterFrontendRoutes(
	r *gin.RouterGroup,
	authHandler *handlers.FrontendAuthHandler,
	socialHandler *handlers.FrontendSocialHandler,
	authMiddleware AuthMiddleware,
) {
	frontendGroup := r.Group("/frontend/auth")

	// Basic authentication routes
	frontendGroup.POST("/register", authHandler.Register)
	frontendGroup.POST("/login", authHandler.Login)
	frontendGroup.POST("/refresh-token", authHandler.RefreshToken)
	// Uncomment these once implemented
	// frontendGroup.POST("/verify-email", authHandler.VerifyEmail)
	// frontendGroup.POST("/forgot-password", authHandler.ForgotPassword)
	// frontendGroup.POST("/reset-password", authHandler.ResetPassword)

	// Social authentication routes
	socialGroup := frontendGroup.Group("/social")

	// Facebook routes
	socialGroup.GET("/facebook/url", socialHandler.GenerateFacebookLoginURL)
	socialGroup.GET("/facebook/callback", socialHandler.HandleFacebookCallback)

	// Google routes
	socialGroup.GET("/google/url", socialHandler.GenerateGoogleLoginURL)
	socialGroup.GET("/google/callback", socialHandler.HandleGoogleCallback)

	// Direct token login (for mobile apps and SPAs)
	socialGroup.POST("/login", socialHandler.HandleSocialLogin)

	// Protected routes (require authentication)
	protectedGroup := frontendGroup.Group("/")
	protectedGroup.Use(authMiddleware.RequireAuth())

	// Social account management routes (require authentication)
	protectedSocialGroup := protectedGroup.Group("/social")
	protectedSocialGroup.GET("/accounts", socialHandler.GetLinkedSocialAccounts)
	protectedSocialGroup.POST("/link", socialHandler.LinkSocialAccount)
	protectedSocialGroup.DELETE("/:provider", socialHandler.UnlinkSocialAccount)
}
