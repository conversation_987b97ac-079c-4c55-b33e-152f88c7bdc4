package core

import (
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"strings"

	"gopkg.in/yaml.v3"
)

// Config quản lý cấu hình <PERSON>ng dụng
type Config struct {
	data     map[string]interface{}
	filePath string
}

// NewConfig tạo đối tượng Config từ file
func NewConfig(filePath string) (*Config, error) {
	cfg := &Config{
		filePath: filePath,
		data:     make(map[string]interface{}),
	}

	// Kiểm tra file có tồn tại không
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return nil, fmt.Errorf("config file %s does not exist: %w", filePath, err)
	}

	// Đọc file cấu hình
	if err := cfg.loadFile(filePath); err != nil {
		return nil, err
	}

	// <PERSON>ử lý imports nếu có
	if err := cfg.processImports(); err != nil {
		return nil, err
	}

	return cfg, nil
}

// loadFile đọc và parse file YAML
func (c *Config) loadFile(filePath string) error {
	// Đọc nội dung file
	data, err := ioutil.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("error reading config file %s: %w", filePath, err)
	}

	// Parse YAML
	var configData map[string]interface{}
	if err := yaml.Unmarshal(data, &configData); err != nil {
		return fmt.Errorf("error parsing config file %s: %w", filePath, err)
	}

	// Merge với data hiện tại
	c.mergeData(configData)

	return nil
}

// processImports xử lý các imports trong file cấu hình
func (c *Config) processImports() error {
	imports, ok := c.data["imports"]
	if !ok {
		return nil // Không có imports
	}

	importsList, ok := imports.([]interface{})
	if !ok {
		return fmt.Errorf("imports must be a list")
	}

	baseDir := filepath.Dir(c.filePath)

	for _, importItem := range importsList {
		importPath, ok := importItem.(string)
		if !ok {
			return fmt.Errorf("import path must be a string")
		}

		// Tạo đường dẫn tuyệt đối nếu là đường dẫn tương đối
		if !filepath.IsAbs(importPath) {
			importPath = filepath.Join(baseDir, importPath)
		}

		// Đọc file import
		if err := c.loadFile(importPath); err != nil {
			return err
		}
	}

	// Xóa trường imports sau khi đã xử lý
	delete(c.data, "imports")

	return nil
}

// mergeData merge cấu hình mới vào cấu hình hiện tại
func (c *Config) mergeData(newData map[string]interface{}) {
	for k, v := range newData {
		// Nếu cả hai là map, merge đệ quy
		if existingVal, ok := c.data[k]; ok {
			if existingMap, ok := existingVal.(map[string]interface{}); ok {
				if newMap, ok := v.(map[string]interface{}); ok {
					for newKey, newVal := range newMap {
						existingMap[newKey] = newVal
					}
					continue
				}
			}
		}
		// Nếu không phải map hoặc key chưa tồn tại, gán trực tiếp
		c.data[k] = v
	}
}

// Get lấy giá trị theo key
func (c *Config) Get(key string) interface{} {
	parts := strings.Split(key, ".")
	var current interface{} = c.data

	for _, part := range parts {
		m, ok := current.(map[string]interface{})
		if !ok {
			return nil
		}

		current, ok = m[part]
		if !ok {
			return nil
		}
	}

	return current
}

// GetString lấy giá trị string theo key
func (c *Config) GetString(key string) string {
	val := c.Get(key)
	if val == nil {
		return ""
	}
	if str, ok := val.(string); ok {
		return str
	}
	return fmt.Sprintf("%v", val)
}

// GetInt lấy giá trị int theo key
func (c *Config) GetInt(key string) int {
	val := c.Get(key)
	if val == nil {
		return 0
	}
	// Xử lý các kiểu số nguyên khác nhau
	switch v := val.(type) {
	case int:
		return v
	case int32:
		return int(v)
	case int64:
		return int(v)
	case float32:
		return int(v)
	case float64:
		return int(v)
	default:
		return 0
	}
}

// GetBool lấy giá trị bool theo key
func (c *Config) GetBool(key string) bool {
	val := c.Get(key)
	if val == nil {
		return false
	}
	if b, ok := val.(bool); ok {
		return b
	}
	// Xử lý string "true"/"false"
	if s, ok := val.(string); ok {
		return strings.ToLower(s) == "true"
	}
	return false
}

// GetStringMap lấy map[string]interface{} theo key
func (c *Config) GetStringMap(key string) map[string]interface{} {
	val := c.Get(key)
	if val == nil {
		return nil
	}
	if m, ok := val.(map[string]interface{}); ok {
		return m
	}
	return nil
}

// GetStringSlice lấy []string theo key
func (c *Config) GetStringSlice(key string) []string {
	val := c.Get(key)
	if val == nil {
		return nil
	}

	if slice, ok := val.([]string); ok {
		return slice
	}

	if slice, ok := val.([]interface{}); ok {
		result := make([]string, len(slice))
		for i, v := range slice {
			result[i] = fmt.Sprintf("%v", v)
		}
		return result
	}

	return nil
}

// UnmarshalKey unmarshal một phần của cấu hình vào đối tượng
func (c *Config) UnmarshalKey(key string, target interface{}) error {
	val := c.Get(key)
	if val == nil {
		return fmt.Errorf("key %s not found in config", key)
	}

	// Convert to YAML và unmarshal
	data, err := yaml.Marshal(val)
	if err != nil {
		return fmt.Errorf("error marshaling config to YAML: %w", err)
	}

	if err := yaml.Unmarshal(data, target); err != nil {
		return fmt.Errorf("error unmarshaling config: %w", err)
	}

	return nil
}
