package main

import (
	"flag"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"time"

	"wnapi/internal/core"
	"wnapi/internal/database"
	"wnapi/internal/pkg/logger"

	// Import modules để đăng ký factories
	_ "wnapi/modules/auth"
	_ "wnapi/modules/hello"

	// Import plugins để đăng ký factories
	_ "wnapi/plugins/logger"

	"github.com/jmoiron/sqlx"
)

var (
	projectFlag = flag.String("project", "", "Project to run migrations for")
	configDir   = flag.String("config", "./config", "Configuration directory")
	forceFlag   = flag.Bool("force", false, "Force migration version")
	versionFlag = flag.Int("version", 0, "Migration version to force")
	dropFlag    = flag.Bool("drop", false, "Drop all migrations")
	actionFlag  = flag.String("action", "up", "Migration action: up, down, version, create")
	moduleFlag  = flag.String("module", "", "Module name for create action")
	nameFlag    = flag.String("name", "", "Migration name for create action")
)

func main() {
	flag.Parse()

	// Handle create action
	if *actionFlag == "create" {
		if *moduleFlag == "" || *nameFlag == "" {
			log.Fatal("Module name and migration name are required for create action")
		}
		createMigration(*moduleFlag, *nameFlag)
		return
	}

	log := logger.NewConsoleLogger("migrate", logger.LevelInfo)
	log.Info("Starting database migration")

	// Nạp cấu hình
	configFilePath := filepath.Join(*configDir, "app.yaml")
	if *projectFlag != "" {
		projectConfigDir := filepath.Join("projects", *projectFlag)
		configFilePath = filepath.Join(projectConfigDir, "config.yaml")
		log.Info("Using project config", "project", *projectFlag, "config", configFilePath)
	}

	cfg, err := core.NewConfig(configFilePath)
	if err != nil {
		log.Error("Failed to load config", "error", err)
		os.Exit(1)
	}

	// Nạp cấu hình database
	dbConfig, err := loadDatabaseConfig(cfg, log)
	if err != nil {
		log.Error("Failed to load database config", "error", err)
		os.Exit(1)
	}

	// Kết nối database
	dbManager, err := database.NewManager(dbConfig, log)
	if err != nil {
		log.Error("Failed to connect to database", "error", err)
		os.Exit(1)
	}
	defer dbManager.Close()

	// Khởi tạo application để đăng ký các module
	app, err := initApp(*projectFlag, configFilePath, dbManager, log)
	if err != nil {
		log.Error("Failed to initialize application", "error", err)
		os.Exit(1)
	}
	defer app.Shutdown() // Sử dụng phương thức Shutdown thay vì Cleanup

	// Handle migrate actions
	switch *actionFlag {
	case "up":
		if err := runMigrationUp(dbManager.DB, dbConfig, app, log); err != nil {
			log.Error("Failed to run migrations", "error", err)
			os.Exit(1)
		}
	case "down":
		if err := runMigrationDown(dbManager.DB, dbConfig, app, log); err != nil {
			log.Error("Failed to rollback migrations", "error", err)
			os.Exit(1)
		}
	case "version":
		if err := showMigrationVersion(dbManager.DB, dbConfig, app, log); err != nil {
			log.Error("Failed to get migration version", "error", err)
			os.Exit(1)
		}
	default:
		log.Error("Unknown action", "action", *actionFlag)
		os.Exit(1)
	}

	log.Info("Migration action completed successfully", "action", *actionFlag)
}

// Khởi tạo app và đăng ký các module
func initApp(projectName, configPath string, dbManager *database.Manager, log logger.Logger) (*core.App, error) {
	// Khởi tạo app với options
	app, err := core.NewApp(core.AppOptions{
		ConfigPath:  configPath,
		ProjectName: projectName,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create app: %w", err)
	}

	// Khởi tạo và đăng ký các module
	if err := app.Initialize(); err != nil {
		return nil, fmt.Errorf("failed to initialize app: %w", err)
	}

	return app, nil
}

// createMigration tạo migration mới
func createMigration(moduleName, migrationName string) {
	generator := &database.MigrationGenerator{
		ModuleName:    moduleName,
		MigrationName: migrationName,
		BasePath:      "migrations",
	}

	if err := generator.Generate(); err != nil {
		log.Fatalf("Failed to create migration: %v", err)
	}

	fmt.Printf("Created migration files for module '%s' with name '%s'\n", moduleName, migrationName)
}

// runMigrationUp chạy migration up
func runMigrationUp(db *sqlx.DB, dbConfig database.Config, app *core.App, log logger.Logger) error {
	// Tạo DSN từ cấu hình
	dsn := getDSN(dbConfig)

	// Lấy db.DB từ *sqlx.DB
	sqlDB := db.DB

	// Tạo registry adapter từ app
	moduleRegistryAdapter := newModuleRegistryAdapter(app)

	migrator, err := database.NewModuleMigrator(sqlDB, &database.MigrationConfig{
		DatabaseURL:    dsn,
		MigrationsPath: "migrations", // Thư mục migrations/ chỉ cho system
		TablePrefix:    "schema",
		Registry:       moduleRegistryAdapter,
	})
	if err != nil {
		return fmt.Errorf("failed to create migrator: %w", err)
	}
	defer migrator.Close()

	// Thêm các module đã đăng ký
	for _, module := range app.GetModules() {
		if err := migrator.AddModule(module.Name()); err != nil {
			return fmt.Errorf("failed to add module %s: %w", module.Name(), err)
		}
	}

	// Chạy migration
	if err := migrator.MigrateUp(); err != nil {
		return fmt.Errorf("migration failed: %w", err)
	}

	return nil
}

// runMigrationDown chạy migration down
func runMigrationDown(db *sqlx.DB, dbConfig database.Config, app *core.App, log logger.Logger) error {
	// Tạo DSN từ cấu hình
	dsn := getDSN(dbConfig)

	// Lấy db.DB từ *sqlx.DB
	sqlDB := db.DB

	// Tạo registry adapter từ app
	moduleRegistryAdapter := newModuleRegistryAdapter(app)

	migrator, err := database.NewModuleMigrator(sqlDB, &database.MigrationConfig{
		DatabaseURL:    dsn,
		MigrationsPath: "migrations", // Thư mục migrations/ chỉ cho system
		TablePrefix:    "schema",
		Registry:       moduleRegistryAdapter,
	})
	if err != nil {
		return fmt.Errorf("failed to create migrator: %w", err)
	}
	defer migrator.Close()

	// Thêm các module đã đăng ký
	for _, module := range app.GetModules() {
		if err := migrator.AddModule(module.Name()); err != nil {
			return fmt.Errorf("failed to add module %s: %w", module.Name(), err)
		}
	}

	// Chạy migration down
	if err := migrator.MigrateDown(); err != nil {
		return fmt.Errorf("migration rollback failed: %w", err)
	}

	return nil
}

// showMigrationVersion hiển thị version của migration
func showMigrationVersion(db *sqlx.DB, dbConfig database.Config, app *core.App, log logger.Logger) error {
	// TODO: Implement migration version
	log.Info("Migration version feature not implemented yet")
	return nil
}

// getDSN tạo connection string từ config
func getDSN(config database.Config) string {
	var dsn string

	switch config.Type {
	case "mysql":
		dsn = fmt.Sprintf("mysql://%s:%s@%s:%d/%s?parseTime=true",
			config.Username, config.Password, config.Host, config.Port, config.Database)
	case "postgres":
		dsn = fmt.Sprintf("postgres://%s:%s@%s:%d/%s?sslmode=disable",
			config.Username, config.Password, config.Host, config.Port, config.Database)
	default:
		dsn = fmt.Sprintf("mysql://%s:%s@%s:%d/%s",
			config.Username, config.Password, config.Host, config.Port, config.Database)
	}

	return dsn
}

// loadDatabaseConfig nạp cấu hình database
func loadDatabaseConfig(cfg *core.Config, log logger.Logger) (database.Config, error) {
	var dbConfig database.Config
	if err := cfg.UnmarshalKey("database", &dbConfig); err != nil {
		return dbConfig, fmt.Errorf("failed to unmarshal database config: %w", err)
	}

	// Thiết lập giá trị mặc định
	if dbConfig.MaxOpenConns == 0 {
		dbConfig.MaxOpenConns = 10
	}
	if dbConfig.MaxIdleConns == 0 {
		dbConfig.MaxIdleConns = 5
	}
	if dbConfig.ConnMaxLifetime == 0 {
		dbConfig.ConnMaxLifetime = 1 * time.Hour
	}
	if dbConfig.MigrationPath == "" {
		dbConfig.MigrationPath = "./migrations"
	}

	return dbConfig, nil
}

// ModuleRegistryAdapter là adapter để chuyển đổi core.App thành database.ModuleRegistry
type ModuleRegistryAdapter struct {
	app *core.App
}

func newModuleRegistryAdapter(app *core.App) *ModuleRegistryAdapter {
	return &ModuleRegistryAdapter{app: app}
}

func (a *ModuleRegistryAdapter) GetModule(name string) (database.ModuleInfo, bool) {
	module, ok := a.app.GetModule(name)
	if !ok {
		return nil, false
	}
	return module, true
}
