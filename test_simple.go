package main

import (
	"context"
	"fmt"
	"wnapi/internal/pkg/tracing"
)

func main() {
	fmt.Println("Testing OpenTelemetry configuration...")

	config := &tracing.Config{
		ServiceName:    "wnapi-test",
		ServiceVersion: "1.0.0",
		Environment:    "test",
		ExporterType:   "console",
	}

	manager := tracing.NewManager(config)

	if err := manager.Start(context.Background()); err != nil {
		fmt.Printf("❌ Tracing failed to start: %v\n", err)
		return
	}
	defer manager.Shutdown(context.Background())

	fmt.Println("✅ OpenTelemetry tracing is working correctly!")
}
