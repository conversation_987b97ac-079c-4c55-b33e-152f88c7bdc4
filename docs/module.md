Tôi sẽ viết lại tài liệu tập trung vào cấu trúc và quy tắc để AI dễ dàng tạo module mới:

# Hướng dẫn Tạo Module Mới

## Cấu trúc Module <PERSON>

```
modules/core/{module_name}/
├── bootstrap.go                 # (Bắt buộc) Khởi tạo dependencies
├── module.go              # (Bắt buộc) Định nghĩa và đăng ký module
├── domain/               # (Bắt buộc) Định nghĩa interfaces và entities
│   ├── entity.go        # Entity chính của module
│   ├── repository.go    # Interface repository
│   ├── service.go       # Interface service
│   └── config.go        # Cấu trúc config
├── repository/          # (Bắt buộc) Implement repository interfaces
│   └── mysql/          # Triển khai cho MySQL
│       └── repository.go
├── service/            # (Bắt buộc) Implement service interfaces
│   └── service.go
└── migrations/         # (Bắt buộc) SQL migrations
    └── mysql/
        ├── XXX_create_table.up.sql
        └── XXX_create_table.down.sql
```

## G<PERSON><PERSON><PERSON> thích Chi Tiết Từng Thư Mục

### 1. File `bootstrap.go`
**Nhiệm vụ**: Khởi tạo và quản lý dependencies cho module theo nguyên tắc Dependency Injection.

**Chức năng chính**:
- Định nghĩa cấu trúc dữ liệu để chứa tất cả dependencies (repositories, services)
- Cung cấp hàm khởi tạo dependencies (`InitializeDependencies`)
- Quản lý vòng đời của các components
- Tạo các factory method để khởi tạo các thành phần khác nhau

**Lưu ý**:
- Nên tách riêng phần khởi tạo repositories và services
- Xử lý lỗi và trả về lỗi rõ ràng khi khởi tạo thất bại
- Cho phép tùy chỉnh cấu hình thông qua tham số

### 2. File `module.go`
**Nhiệm vụ**: Định nghĩa và đăng ký module với hệ thống core.

**Chức năng chính**:
- Đăng ký module factory với registry toàn cục (qua hàm `init`)
- Định nghĩa struct `Module` triển khai interface `core.Module`
- Triển khai các phương thức của module lifecycle:
  - `Init`: Khởi tạo module
  - `RegisterRoutes`: Đăng ký API routes
  - `Cleanup`: Giải phóng tài nguyên
  - `GetMigrationPath`: Trả về đường dẫn migrations
  - `GetMigrationOrder`: Xác định thứ tự ưu tiên migrations

**Lưu ý**:
- Đảm bảo xử lý lỗi đúng cách trong tất cả các phương thức
- Sử dụng logger để ghi nhật ký quá trình khởi động/tắt module
- Đảm bảo việc khởi tạo các dependency là lazy (chỉ khi cần)

### 3. Thư mục `domain/`
**Nhiệm vụ**: Định nghĩa core business logic, interfaces và entities, áp dụng Domain-Driven Design.

**Thành phần chính**:
- **`entity.go`**: Định nghĩa các entity (model) chính của module, đại diện cho các đối tượng business
- **`repository.go`**: Định nghĩa các interface repository xử lý truy xuất dữ liệu
- **`service.go`**: Định nghĩa các interface service chứa business logic
- **`config.go`**: Định nghĩa cấu trúc cấu hình cho module và các service

**Chi tiết về Domain Layer**:
1. **Nguyên tắc thiết kế**:
   - **Độc lập với infrastructure**: Domain không nên phụ thuộc vào database, HTTP, frameworks
   - **Nguyên tắc SOLID**: Áp dụng Single Responsibility, Open/Closed, Liskov Substitution, Interface Segregation, Dependency Inversion
   - **Tách biệt concerns**: Entity, Value Objects, Aggregates, Domain Services, Repositories

2. **Cấu trúc Entity**:
   - Bao gồm các thuộc tính (properties) đặc trưng cho business object
   - Bao gồm các phương thức cho logic liên quan đến entity
   - Định nghĩa các invariants và validation rules
   - Không chứa logic truy cập database

3. **Interface Repository**:
   - Định nghĩa các phương thức CRUD cơ bản
   - Thêm các phương thức truy vấn đặc thù cho business cụ thể
   - Sử dụng pattern Repository để trừu tượng hóa việc truy cập dữ liệu
   - Hỗ trợ cursor-based pagination

4. **Interface Service**:
   - Điều phối các tác vụ nghiệp vụ phức tạp
   - Định nghĩa các phương thức phản ánh use cases
   - Xác định dữ liệu input/output rõ ràng
   - Context và error handling

5. **Domain Events (nếu cần)**:
   - Định nghĩa các event xảy ra trong domain
   - Sử dụng pattern Event Sourcing hoặc Domain Events
   - Giúp tách biệt các concerns xử lý side-effects

**Lưu ý**:
- Không nên có phụ thuộc vào infrastructure (database, HTTP, v.v.) trong domain
- Các interface nên rõ ràng, chỉ chứa các phương thức thực sự cần thiết
- Entity nên bao gồm cả validation logic
- Định nghĩa rõ ràng các kiểu dữ liệu input/output

### 4. Thư mục `repository/`
**Nhiệm vụ**: Triển khai các interface repository được định nghĩa trong domain.

**Thành phần chính**:
- **`mysql/`**: Chứa triển khai repository cho MySQL
  - **`repository.go`**: Triển khai các phương thức của repository interface

**Lưu ý**:
- Xử lý các lỗi cơ sở dữ liệu và chuyển đổi thành domain error
- Đảm bảo sử dụng prepared statements để tránh SQL injection
- Sử dụng transactions khi cần thiết
- Tối ưu hóa truy vấn SQL
- Đảm bảo đóng tất cả các kết nối và giải phóng tài nguyên

### 5. Thư mục `service/`
**Nhiệm vụ**: Triển khai các interface service định nghĩa trong domain, chứa business logic.

**Thành phần chính**:
- **`service.go`**: Triển khai các phương thức của service interface

**Chi tiết về Service Layer**:
1. **Nguyên tắc thiết kế**:
   - **Single Responsibility**: Mỗi service chỉ nên thực hiện một nhiệm vụ rõ ràng
   - **Dependency Injection**: Nhận các dependencies qua constructor
   - **Stateless**: Không lưu trữ trạng thái giữa các lần gọi method
   - **Transaction handling**: Quản lý transactions khi cần thiết

2. **Cấu trúc Service**:
   - Nhận các repository dependencies qua constructor
   - Nhận cấu hình từ config
   - Triển khai các use cases theo yêu cầu nghiệp vụ
   - Thực hiện validation business rules
   - Xử lý domain events (nếu có)

3. **Error Handling**:
   - Định nghĩa rõ ràng các loại lỗi có thể xảy ra
   - Sử dụng `fmt.Errorf("context: %w", err)` để wrap errors
   - Tạo custom error types khi cần thiết
   - Không làm mất thông tin lỗi gốc khi wrap errors

4. **Logging và Metrics**:
   - Log các operations quan trọng
   - Tạo metrics cho performance monitoring
   - Đảm bảo log đủ thông tin để debug
   - Không log thông tin nhạy cảm

5. **Testing**:
   - Sử dụng mocks cho repository dependencies
   - Test các business rules và error handling
   - Kiểm tra các edge cases
   - Sử dụng table-driven tests

**Lưu ý**:
- Triển khai tất cả business logic và validation
- Không truy cập database trực tiếp, luôn thông qua repository
- Xử lý lỗi đúng cách và wrap lỗi với context
- Đảm bảo không có side effects không mong muốn
- Sử dụng logger để ghi log các operation quan trọng

### 6. Thư mục `migrations/`
**Nhiệm vụ**: Chứa các script SQL migration để quản lý schema cơ sở dữ liệu.

**Thành phần chính**:
- **`mysql/`**: Chứa các file migration cho MySQL
  - **`XXX_create_table.up.sql`**: Script để tạo/cập nhật bảng (XXX là số thứ tự)
  - **`XXX_create_table.down.sql`**: Script để rollback migration

**Lưu ý**:
- Đặt tên các file theo quy tắc `[số thứ tự]_[mô tả].[up/down].sql`
- Luôn có cả file `.up.sql` và `.down.sql` tương ứng
- Đảm bảo các script là idempotent (có thể chạy nhiều lần mà không gây lỗi)
- Sử dụng các ràng buộc (constraints) hợp lý
- Đảm bảo các migrations không làm mất dữ liệu

### 7. Thư mục `api/` (tuỳ chọn)
**Nhiệm vụ**: Xử lý HTTP requests, định nghĩa API endpoints và serialization.

**Thành phần chính**:
- **`handler.go`**: Xử lý HTTP requests, triển khai các endpoint
- **`router.go`**: Đăng ký routes cho module
- **`middleware.go`**: Định nghĩa các middleware (nếu cần)

**Lưu ý**:
- Tách biệt logic API khỏi business logic
- Thực hiện validation input
- Đảm bảo response format chuẩn
- Xử lý lỗi và trả về mã lỗi HTTP phù hợp

### 8. Thư mục `dto/` (tuỳ chọn)
**Nhiệm vụ**: Định nghĩa Data Transfer Objects (DTOs) để giao tiếp giữa client và server.

**Thành phần chính**:
- **`request.go`**: Định nghĩa các struct request
- **`response.go`**: Định nghĩa các struct response

**Lưu ý**:
- DTOs nên tách biệt với domain entities
- Thêm validation tags (như `validate:"required"`)
- Đảm bảo đặt tên field JSON đúng chuẩn (thường là camelCase)

## Quy tắc tạo file

### 1. bootstrap.go
```go
package {module_name}

type Dependencies struct {
    Repositories *Repositories
    Services     *Services
}

type Repositories struct {
    MainRepository domain.Repository
    // Thêm các repository khác
}

type Services struct {
    MainService domain.Service  
    // Thêm các service khác
}

func InitializeDependencies(db *sql.DB) (*Dependencies, error) {
    repositories := initializeRepositories(db)
    services := initializeServices(repositories)
    return &Dependencies{
        Repositories: repositories,
        Services: services,
    }, nil
}
```

### 2. module.go
```go
package {module_name}

func init() {
    core.RegisterModuleFactory("{module_name}", NewModule)
}

type Module struct {
    name       string
    logger     logger.Logger
    config     map[string]interface{}
    app        *core.App
    components *ModuleComponents
}

func NewModule(app *core.App, config map[string]interface{}) (core.Module, error) {
    return &Module{
        name:   "{module_name}",
        logger: app.GetLogger(),
        config: config,
        app:    app,
    }, nil
}

// Implement các method bắt buộc: Init(), RegisterRoutes(), Cleanup()
```

### 3. domain/entity.go
```go
package domain

type Entity struct {
    ID        uint      `json:"id" gorm:"primaryKey"`
    CreatedAt time.Time `json:"created_at"`
    UpdatedAt time.Time `json:"updated_at"`
    // Thêm các trường khác
}
```

### 4. domain/repository.go
```go
package domain

type Repository interface {
    Create(ctx context.Context, entity *Entity) error
    Update(ctx context.Context, entity *Entity) error
    Delete(ctx context.Context, id uint) error
    GetByID(ctx context.Context, id uint) (*Entity, error)
    List(ctx context.Context, cursor string, limit int) ([]*Entity, string, error)
}
```

### 5. domain/service.go
```go
package domain

type Service interface {
    Create(ctx context.Context, input CreateInput) (*Entity, error)
    Update(ctx context.Context, input UpdateInput) (*Entity, error)
    Delete(ctx context.Context, id uint) error
    GetByID(ctx context.Context, id uint) (*Entity, error)
    List(ctx context.Context, cursor string, limit int) ([]*Entity, string, error)
}
```

### 6. repository/mysql/repository.go
```go
package mysql

type repository struct {
    db     *sql.DB
    logger logger.Logger
}

func NewRepository(db *sql.DB, logger logger.Logger) domain.Repository {
    return &repository{
        db:     db,
        logger: logger,
    }
}

// Implement các method của interface Repository
```

### 7. service/service.go
```go
package service

type service struct {
    config     *domain.Config
    repository domain.Repository
    logger     logger.Logger
}

func NewService(config *domain.Config, repository domain.Repository) domain.Service {
    return &service{
        config:     config,
        repository: repository,
    }
}

// Implement các method của interface Service
```

### 8. migrations/mysql/XXX_create_table.up.sql
```sql
CREATE TABLE {module_name} (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    -- Thêm các cột khác
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## Quy tắc quan trọng

1. **Tên Module**
   - Sử dụng snake_case cho tên thư mục
   - Sử dụng camelCase cho tên package Go

2. **Database**
   - Luôn sử dụng `INT UNSIGNED` cho ID
   - Luôn có `created_at` và `updated_at`
   - Sử dụng `utf8mb4` và `utf8mb4_unicode_ci`

3. **Response Format**
```go
// Success
{
    "status": {
        "code": 200,
        "message": "Operation completed successfully",
        "success": true,
        "error_code": null,
        "path": "/api/{module_name}",
        "timestamp": "2024-03-15T14:35:22Z"
    },
    "data": [] // hoặc {}
}

// Error
{
    "status": {
        "code": 400,
        "message": "Error message",
        "success": false,
        "error_code": "ERROR_CODE",
        "path": "/api/{module_name}",
        "timestamp": "2024-03-15T14:35:22Z"
    }
}
```

4. **Logging**
   - Sử dụng logger từ app
   - Log tất cả các operations quan trọng

5. **Context**
   - Luôn truyền context trong các method
   - Sử dụng context để cancel operations

6. **Error Handling**
   - Định nghĩa error codes trong domain
   - Wrap errors với context
   - Sử dụng error response chuẩn

7. **Configuration**
   - Đọc config từ `config.yaml`
   - Validate config khi khởi tạo

8. **Testing**
   - Viết tests cho tất cả các interfaces
   - Sử dụng mock cho dependencies

Với cấu trúc và quy tắc này, AI có thể dễ dàng tạo một module mới tuân thủ các tiêu chuẩn của hệ thống.
