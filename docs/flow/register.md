# User Register - Complete Implementation Example

## 1. Service Layer

### service/user.go - Entity và business rules cơ b<PERSON>n
```go
package service

import (
    "errors"
    "time"
    "golang.org/x/crypto/bcrypt"
)

type UserStatus string

const (
    UserStatusPending   UserStatus = "pending"
    UserStatusActive    UserStatus = "active"
    UserStatusInactive  UserStatus = "inactive"
    UserStatusSuspended UserStatus = "suspended"
)

type User struct {
    ID            int64      `json:"id" db:"id"`
    TenantID      int64      `json:"tenant_id" db:"tenant_id"`
    WebsiteID     *int64     `json:"website_id" db:"website_id"`
    Username      string     `json:"username" db:"username"`
    Email         string     `json:"email" db:"email"`
    EmailVerified bool       `json:"email_verified" db:"email_verified"`
    PasswordHash  string     `json:"-" db:"password_hash"`
    Status        UserStatus `json:"status" db:"status"`
    CreatedAt     time.Time  `json:"created_at" db:"created_at"`
    UpdatedAt     time.Time  `json:"updated_at" db:"updated_at"`
}

// Business rules trong entity
func (u *User) SetPassword(password string) error {
    if len(password) < 8 {
        return errors.New("password must be at least 8 characters")
    }
    
    hash, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
    if err != nil {
        return err
    }
    
    u.PasswordHash = string(hash)
    return nil
}

func (u *User) CheckPassword(password string) error {
    return bcrypt.CompareHashAndPassword([]byte(u.PasswordHash), []byte(password))
}

func (u *User) IsActive() bool {
    return u.Status == UserStatusActive
}

func (u *User) CanLogin() bool {
    return u.IsActive() && u.EmailVerified
}

func (u *User) Activate() {
    u.Status = UserStatusActive
    u.EmailVerified = true
    u.UpdatedAt = time.Now()
}
```

### service/interfaces.go - Repository interfaces
```go
package service

import "context"

type UserRepository interface {
    Create(ctx context.Context, user *User) error
    GetByEmail(ctx context.Context, tenantID int64, email string) (*User, error)
    GetByUsername(ctx context.Context, tenantID int64, username string) (*User, error)
    GetByID(ctx context.Context, tenantID int64, userID int64) (*User, error)
    Update(ctx context.Context, user *User) error
    ExistsBy(ctx context.Context, tenantID int64, field string, value string) (bool, error)
}

type EmailService interface {
    SendVerificationEmail(ctx context.Context, email, token string) error
}

type EventPublisher interface {
    Publish(ctx context.Context, event string, data interface{}) error
}
```

### service/user_service.go - Business logic service
```go
package service

import (
    "context"
    "errors"
    "fmt"
    "regexp"
    "time"
    "crypto/rand"
    "encoding/base64"
)

var (
    ErrUserAlreadyExists    = errors.New("user already exists")
    ErrInvalidEmail        = errors.New("invalid email format")
    ErrInvalidUsername     = errors.New("invalid username format")
    ErrWeakPassword       = errors.New("password is too weak")
    ErrUserNotFound       = errors.New("user not found")
)

type UserService struct {
    userRepo     UserRepository
    emailService EmailService
    eventBus     EventPublisher
}

func NewUserService(userRepo UserRepository, emailService EmailService, eventBus EventPublisher) *UserService {
    return &UserService{
        userRepo:     userRepo,
        emailService: emailService,
        eventBus:     eventBus,
    }
}

type RegisterUserRequest struct {
    TenantID  int64   `json:"tenant_id"`
    WebsiteID *int64  `json:"website_id,omitempty"`
    Username  string  `json:"username"`
    Email     string  `json:"email"`
    Password  string  `json:"password"`
    FirstName *string `json:"first_name,omitempty"`
    LastName  *string `json:"last_name,omitempty"`
}

type RegisterUserResponse struct {
    User    *User  `json:"user"`
    Token   string `json:"verification_token,omitempty"`
    Message string `json:"message"`
}

func (s *UserService) RegisterUser(ctx context.Context, req *RegisterUserRequest) (*RegisterUserResponse, error) {
    // 1. Validate input
    if err := s.validateRegisterRequest(req); err != nil {
        return nil, err
    }
    
    // 2. Check if user already exists
    if err := s.checkUserExists(ctx, req); err != nil {
        return nil, err
    }
    
    // 3. Create user entity
    user := &User{
        TenantID:      req.TenantID,
        WebsiteID:     req.WebsiteID,
        Username:      req.Username,
        Email:         req.Email,
        EmailVerified: false,
        Status:        UserStatusPending,
        CreatedAt:     time.Now(),
        UpdatedAt:     time.Now(),
    }
    
    // 4. Set password
    if err := user.SetPassword(req.Password); err != nil {
        return nil, fmt.Errorf("failed to set password: %w", err)
    }
    
    // 5. Save user to database
    if err := s.userRepo.Create(ctx, user); err != nil {
        return nil, fmt.Errorf("failed to create user: %w", err)
    }
    
    // 6. Generate verification token
    verificationToken, err := s.generateVerificationToken()
    if err != nil {
        return nil, fmt.Errorf("failed to generate verification token: %w", err)
    }
    
    // 7. Send verification email
    if err := s.emailService.SendVerificationEmail(ctx, user.Email, verificationToken); err != nil {
        // Log error but don't fail registration
        // In production, you might want to queue this for retry
        fmt.Printf("Failed to send verification email: %v\n", err)
    }
    
    // 8. Publish user registered event
    if err := s.eventBus.Publish(ctx, "user.registered", &UserRegisteredEvent{
        UserID:   user.ID,
        TenantID: user.TenantID,
        Email:    user.Email,
        Username: user.Username,
    }); err != nil {
        // Log error but don't fail registration
        fmt.Printf("Failed to publish user registered event: %v\n", err)
    }
    
    return &RegisterUserResponse{
        User:    user,
        Token:   verificationToken,
        Message: "User registered successfully. Please check your email for verification.",
    }, nil
}

func (s *UserService) validateRegisterRequest(req *RegisterUserRequest) error {
    // Validate email format
    if !s.isValidEmail(req.Email) {
        return ErrInvalidEmail
    }
    
    // Validate username format
    if !s.isValidUsername(req.Username) {
        return ErrInvalidUsername
    }
    
    // Validate password strength
    if !s.isStrongPassword(req.Password) {
        return ErrWeakPassword
    }
    
    return nil
}

func (s *UserService) checkUserExists(ctx context.Context, req *RegisterUserRequest) error {
    // Check email exists
    emailExists, err := s.userRepo.ExistsBy(ctx, req.TenantID, "email", req.Email)
    if err != nil {
        return fmt.Errorf("failed to check email existence: %w", err)
    }
    if emailExists {
        return fmt.Errorf("email already exists: %w", ErrUserAlreadyExists)
    }
    
    // Check username exists
    usernameExists, err := s.userRepo.ExistsBy(ctx, req.TenantID, "username", req.Username)
    if err != nil {
        return fmt.Errorf("failed to check username existence: %w", err)
    }
    if usernameExists {
        return fmt.Errorf("username already exists: %w", ErrUserAlreadyExists)
    }
    
    return nil
}

func (s *UserService) isValidEmail(email string) bool {
    emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
    return emailRegex.MatchString(email)
}

func (s *UserService) isValidUsername(username string) bool {
    // Username: 3-30 characters, alphanumeric + underscore, no spaces
    if len(username) < 3 || len(username) > 30 {
        return false
    }
    usernameRegex := regexp.MustCompile(`^[a-zA-Z0-9_]+$`)
    return usernameRegex.MatchString(username)
}

func (s *UserService) isStrongPassword(password string) bool {
    if len(password) < 8 {
        return false
    }
    
    // Check for at least one uppercase, lowercase, and number
    hasUpper := regexp.MustCompile(`[A-Z]`).MatchString(password)
    hasLower := regexp.MustCompile(`[a-z]`).MatchString(password)
    hasNumber := regexp.MustCompile(`[0-9]`).MatchString(password)
    
    return hasUpper && hasLower && hasNumber
}

func (s *UserService) generateVerificationToken() (string, error) {
    bytes := make([]byte, 32)
    if _, err := rand.Read(bytes); err != nil {
        return "", err
    }
    return base64.URLEncoding.EncodeToString(bytes), nil
}

// Event struct
type UserRegisteredEvent struct {
    UserID   int64  `json:"user_id"`
    TenantID int64  `json:"tenant_id"`
    Email    string `json:"email"`
    Username string `json:"username"`
}
```

## 2. Repository Layer

### repository/mysql/user_repository.go - Data access implementation
```go
package mysql

import (
    "context"
    "database/sql"
    "fmt"
    "time"
    
    "myapp/modules/user/service"
    "gorm.io/gorm"
)

type UserRepository struct {
    db *gorm.DB
}

func NewUserRepository(db *gorm.DB) *UserRepository {
    return &UserRepository{db: db}
}

func (r *UserRepository) Create(ctx context.Context, user *service.User) error {
    user.CreatedAt = time.Now()
    user.UpdatedAt = time.Now()
    
    return r.db.WithContext(ctx).Create(user).Error
}

func (r *UserRepository) GetByEmail(ctx context.Context, tenantID int64, email string) (*service.User, error) {
    var user service.User
    err := r.db.WithContext(ctx).
        Where("tenant_id = ? AND email = ? AND deleted_at IS NULL", tenantID, email).
        First(&user).Error
    
    if err != nil {
        if err == gorm.ErrRecordNotFound {
            return nil, service.ErrUserNotFound
        }
        return nil, err
    }
    
    return &user, nil
}

func (r *UserRepository) GetByUsername(ctx context.Context, tenantID int64, username string) (*service.User, error) {
    var user service.User
    err := r.db.WithContext(ctx).
        Where("tenant_id = ? AND username = ? AND deleted_at IS NULL", tenantID, username).
        First(&user).Error
    
    if err != nil {
        if err == gorm.ErrRecordNotFound {
            return nil, service.ErrUserNotFound
        }
        return nil, err
    }
    
    return &user, nil
}

func (r *UserRepository) GetByID(ctx context.Context, tenantID int64, userID int64) (*domain.User, error) {
    var user domain.User
    err := r.db.WithContext(ctx).
        Where("tenant_id = ? AND id = ? AND deleted_at IS NULL", tenantID, userID).
        First(&user).Error
    
    if err != nil {
        if err == gorm.ErrRecordNotFound {
            return nil, domain.ErrUserNotFound
        }
        return nil, err
    }
    
    return &user, nil
}

func (r *UserRepository) Update(ctx context.Context, user *domain.User) error {
    user.UpdatedAt = time.Now()
    return r.db.WithContext(ctx).Save(user).Error
}

func (r *UserRepository) ExistsBy(ctx context.Context, tenantID int64, field, value string) (bool, error) {
    var count int64
    
    query := fmt.Sprintf("tenant_id = ? AND %s = ? AND deleted_at IS NULL", field)
    err := r.db.WithContext(ctx).
        Model(&domain.User{}).
        Where(query, tenantID, value).
        Count(&count).Error
    
    return count > 0, err
}
```

## 3. DTO Layer

### dto/user_dto.go - Data Transfer Objects
```go
package dto

import "time"

type RegisterRequest struct {
    Username  string  `json:"username" binding:"required,min=3,max=30"`
    Email     string  `json:"email" binding:"required,email"`
    Password  string  `json:"password" binding:"required,min=8"`
    FirstName *string `json:"first_name,omitempty"`
    LastName  *string `json:"last_name,omitempty"`
}

type RegisterResponse struct {
    ID       int64  `json:"id"`
    Username string `json:"username"`
    Email    string `json:"email"`
    Status   string `json:"status"`
    Message  string `json:"message"`
}

type UserResponse struct {
    ID            int64     `json:"id"`
    Username      string    `json:"username"`
    Email         string    `json:"email"`
    EmailVerified bool      `json:"email_verified"`
    Status        string    `json:"status"`
    CreatedAt     time.Time `json:"created_at"`
}
```

## 4. API Layer

### api/frontend/auth_handler.go - HTTP handlers
```go
package frontend

import (
    "net/http"
    "strconv"
    
    "myapp/modules/user/domain"
    "myapp/modules/user/dto"
    "myapp/internal/pkg/response"
    "github.com/gin-gonic/gin"
)

type AuthHandler struct {
    userService *domain.UserService
}

func NewAuthHandler(userService *domain.UserService) *AuthHandler {
    return &AuthHandler{
        userService: userService,
    }
}

func (h *AuthHandler) Register(c *gin.Context) {
    var req dto.RegisterRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        response.Error(c, http.StatusBadRequest, "Invalid request data", err)
        return
    }
    
    // Get tenant ID from context (set by tenant middleware)
    tenantID, exists := c.Get("tenant_id")
    if !exists {
        response.Error(c, http.StatusBadRequest, "Tenant not found", nil)
        return
    }
    
    // Get website ID from context if available
    var websiteID *int64
    if wid, exists := c.Get("website_id"); exists {
        if id, ok := wid.(int64); ok {
            websiteID = &id
        }
    }
    
    // Convert DTO to domain request
    registerReq := &domain.RegisterUserRequest{
        TenantID:  tenantID.(int64),
        WebsiteID: websiteID,
        Username:  req.Username,
        Email:     req.Email,
        Password:  req.Password,
        FirstName: req.FirstName,
        LastName:  req.LastName,
    }
    
    // Call domain service
    result, err := h.userService.RegisterUser(c.Request.Context(), registerReq)
    if err != nil {
        h.handleRegisterError(c, err)
        return
    }
    
    // Convert to response DTO
    resp := &dto.RegisterResponse{
        ID:       result.User.ID,
        Username: result.User.Username,
        Email:    result.User.Email,
        Status:   string(result.User.Status),
        Message:  result.Message,
    }
    
    response.Success(c, http.StatusCreated, "User registered successfully", resp)
}

func (h *AuthHandler) handleRegisterError(c *gin.Context, err error) {
    switch err {
    case domain.ErrUserAlreadyExists:
        response.Error(c, http.StatusConflict, "User already exists", err)
    case domain.ErrInvalidEmail:
        response.Error(c, http.StatusBadRequest, "Invalid email format", err)
    case domain.ErrInvalidUsername:
        response.Error(c, http.StatusBadRequest, "Invalid username format", err)
    case domain.ErrWeakPassword:
        response.Error(c, http.StatusBadRequest, "Password is too weak", err)
    default:
        response.Error(c, http.StatusInternalServerError, "Internal server error", err)
    }
}
```

### api/frontend/router.go - Routes definition
```go
package frontend

import (
    "myapp/modules/user/domain"
    "github.com/gin-gonic/gin"
)

func RegisterRoutes(r *gin.Engine, userService *domain.UserService) {
    authHandler := NewAuthHandler(userService)
    
    // Public routes (no authentication required)
    public := r.Group("/api/frontend/v1")
    {
        public.POST("/auth/register", authHandler.Register)
        public.POST("/auth/login", authHandler.Login)
        public.POST("/auth/forgot-password", authHandler.ForgotPassword)
        public.POST("/auth/reset-password", authHandler.ResetPassword)
        public.POST("/auth/verify-email", authHandler.VerifyEmail)
    }
    
    // Protected routes (authentication required)
    protected := r.Group("/api/frontend/v1")
    protected.Use(authMiddleware)
    {
        protected.POST("/auth/logout", authHandler.Logout)
        protected.POST("/auth/refresh", authHandler.RefreshToken)
        protected.GET("/profile", authHandler.GetProfile)
        protected.PUT("/profile", authHandler.UpdateProfile)
    }
}
```

## 5. Response Helper

### internal/pkg/response/response.go
```go
package response

import (
    "github.com/gin-gonic/gin"
)

type Response struct {
    Success bool        `json:"success"`
    Message string      `json:"message"`
    Data    interface{} `json:"data,omitempty"`
    Error   interface{} `json:"error,omitempty"`
}

func Success(c *gin.Context, statusCode int, message string, data interface{}) {
    c.JSON(statusCode, Response{
        Success: true,
        Message: message,
        Data:    data,
    })
}

func Error(c *gin.Context, statusCode int, message string, err error) {
    resp := Response{
        Success: false,
        Message: message,
    }
    
    if err != nil {
        resp.Error = err.Error()
    }
    
    c.JSON(statusCode, resp)
}
```

## 6. Usage Example

### Cách sử dụng API:

```bash
# Register new user
curl -X POST http://localhost:8080/api/frontend/v1/auth/register \
  -H "Content-Type: application/json" \
  -H "X-Tenant-ID: 1" \
  -d '{
    "username": "john_doe",
    "email": "<EMAIL>",
    "password": "SecurePass123",
    "first_name": "John",
    "last_name": "Doe"
  }'

# Response:
{
  "success": true,
  "message": "User registered successfully",
  "data": {
    "id": 1,
    "username": "john_doe",
    "email": "<EMAIL>",
    "status": "pending",
    "message": "User registered successfully. Please check your email for verification."
  }
}
```