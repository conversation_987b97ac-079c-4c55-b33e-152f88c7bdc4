# Tổng hợp Cấu trúc Module Blog và E-commerce

Dưới đây là cấu trúc tổng hợp cho các module Blog và E-commerce phù hợp với dự án Golang microservices mô-đun hóa:
modules/
├── tenant/                              # Module quản lý tenant
│   ├── module.go
│   ├── api/
│   │   ├── handler.go
│   │   ├── middleware.go
│   │   └── router.go
│   ├── domain/
│   │   ├── model.go
│   │   └── service.go
│   ├── repository/
│   │   ├── repository.go
│   │   └── mysql_repository.go
│   └── migrations/
│       └── 001_create_tenant_tables.sql
│
├── website/                             # Module quản lý nhiều website cho tenant
│   ├── module.go                        # Khai báo & khởi tạo module
│   ├── api/                             # API handlers
│   │   ├── handler.go                   # Website API handlers
│   │   ├── middleware.go                # Middleware xác định website
│   │   └── router.go                    # Định nghĩa routes
│   ├── domain/                          # Business logic
│   │   ├── model.go                     # Website model
│   │   └── service.go                   # Website service
│   ├── repository/                      # Data access layer
│   │   ├── repository.go                # Repository interface
│   │   └── mysql_repository.go          # MySQL implementation
│   ├── dto/                             # Data Transfer Objects
│   │   └── website_dto.go               # Website DTOs
│   └── migrations/                      # Database migrations
│       └── 001_create_website_tables.sql
│
├── theme/                               # Module quản lý theme cho website
│   ├── module.go                        # Khai báo & khởi tạo module
│   ├── api/                             # API handlers
│   │   ├── handler.go                   # Theme API handlers
│   │   └── router.go                    # Định nghĩa routes
│   ├── domain/                          # Business logic
│   │   ├── model.go                     # Theme model
│   │   └── service.go                   # Theme service
│   ├── repository/                      # Data access layer
│   │   ├── repository.go                # Repository interface
│   │   └── mysql_repository.go          # MySQL implementation
│   ├── dto/                             # Data Transfer Objects
│   │   └── theme_dto.go                 # Theme DTOs
│   └── migrations/                      # Database migrations
│       └── 001_create_theme_tables.sql
## Module Blog

```
modules/
├── blog/                                # Module blog
│   ├── module.go                        # Khai báo & khởi tạo module
│   │
│   ├── api/                             # Layer xử lý API requests
│   │   ├── handler.go                   # API handlers chính
│   │   ├── post_handler.go              # Xử lý API cho bài viết
│   │   ├── category_handler.go          # Xử lý API cho danh mục
│   │   ├── comment_handler.go           # Xử lý API cho bình luận
│   │   ├── tag_handler.go               # Xử lý API cho tags
│   │   ├── middleware.go                # Middleware riêng của blog module
│   │   └── router.go                    # Định nghĩa routes
│   │
│   ├── service/                          # Business logic layer
│   │   ├── entity.go                     # Service entities
│   │   ├── post.go                      # Post entity
│   │   ├── category.go                  # Category entity
│   │   ├── comment.go                   # Comment entity
│   │   ├── tag.go                       # Tag entity
│   │   ├── service.go                   # Interface services
│   │   ├── post_service.go              # Post business logic
│   │   ├── category_service.go          # Category business logic
│   │   ├── comment_service.go           # Comment business logic
│   │   └── tag_service.go               # Tag business logic
│   │
│   ├── repository/                      # Data access layer
│   │   ├── repository.go                # Repository interfaces
│   │   ├── post_repository.go           # Post repository interface
│   │   ├── category_repository.go       # Category repository interface
│   │   ├── comment_repository.go        # Comment repository interface
│   │   ├── tag_repository.go            # Tag repository interface
│   │   ├── mysql/                       # MySQL implementations
│   │   │   ├── post_repository.go       # Post MySQL repository
│   │   │   ├── category_repository.go   # Category MySQL repository
│   │   │   ├── comment_repository.go    # Comment MySQL repository
│   │   │   └── tag_repository.go        # Tag MySQL repository
│   │   └── cache/                       # Cache implementations (optional)
│   │       ├── post_cache.go            # Post cache repository
│   │       └── category_cache.go        # Category cache repository
│   │
│   ├── dto/                             # Data Transfer Objects
│   │   ├── post_dto.go                  # Post DTOs (create/update/response)
│   │   ├── category_dto.go              # Category DTOs
│   │   ├── comment_dto.go               # Comment DTOs
│   │   ├── tag_dto.go                   # Tag DTOs
│   │   └── filter_dto.go                # Query/filter DTOs
│   │
│   └── migrations/                      # Database migrations
│       ├── 001_create_post_tables.sql   # Tạo bảng posts
│       ├── 002_create_category_tables.sql # Tạo bảng categories
│       ├── 003_create_tag_tables.sql    # Tạo bảng tags
│       ├── 004_create_comment_tables.sql # Tạo bảng comments
│       └── 005_create_post_tag_tables.sql # Tạo bảng quan hệ post_tags
```

## Module E-commerce

```
modules/
├── product/                            # Module quản lý sản phẩm
│   ├── module.go                       # Khai báo & khởi tạo module
│   ├── api/                            # API handlers
│   │   ├── handler.go                  # Xử lý các request API
│   │   ├── middleware.go               # Middleware riêng của sản phẩm (nếu cần)
│   │   └── router.go                   # Định nghĩa routes của product
│   ├── service/                         # Business logic
│   │   ├── entity.go                    # Service entities (Product, Category, Review...)
│   │   └── service.go                  # Business service (ProductService)
│   ├── repository/                     # Data access layer
│   │   ├── repository.go               # Interface repository
│   │   └── mysql_repository.go         # MySQL implementation
│   ├── dto/                            # Data Transfer Objects
│   │   ├── product_dto.go              # Request/Response DTOs
│   │   └── filter_dto.go               # Search/filter DTOs
│   └── migrations/                     # Database migrations
│       ├── 001_create_products.sql
│       └── 002_create_categories.sql
│
├── cart/                               # Module giỏ hàng
│   ├── module.go
│   ├── api/
│   │   ├── handler.go  
│   │   └── router.go
│   ├── service/
│   │   ├── entity.go                    # Cart, CartItem entities
│   │   └── service.go                  # CartService
│   ├── repository/
│   │   ├── repository.go
│   │   └── mysql_repository.go
│   ├── dto/
│   │   └── cart_dto.go
│   └── migrations/
│       └── 001_create_carts.sql
│
├── order/                              # Module đơn hàng
│   ├── module.go
│   ├── api/
│   │   ├── handler.go
│   │   └── router.go
│   ├── service/
│   │   ├── entity.go                    # Order, OrderItem, OrderStatus entities
│   │   └── service.go                  # OrderService
│   ├── repository/
│   │   ├── repository.go
│   │   └── mysql_repository.go
│   ├── dto/
│   │   ├── order_dto.go
│   │   └── order_filter_dto.go
│   └── migrations/
│       ├── 001_create_orders.sql
│       └── 002_create_order_items.sql
│
├── shipping/                           # Module vận chuyển
│   ├── module.go
│   ├── api/
│   │   ├── handler.go
│   │   └── router.go
│   ├── service/
│   │   ├── entity.go                    # ShippingMethod, ShippingZone entities
│   │   └── service.go                  # ShippingService
│   ├── repository/
│   │   ├── repository.go
│   │   └── mysql_repository.go
│   ├── dto/
│   │   └── shipping_dto.go
│   └── migrations/
│       └── 001_create_shipping.sql
│
├── discount/                           # Module khuyến mãi/mã giảm giá
│   ├── module.go
│   ├── api/
│   │   ├── handler.go
│   │   └── router.go
│   ├── service/
│   │   ├── entity.go                    # Coupon, Discount entities
│   │   └── service.go                  # DiscountService
│   ├── repository/
│   │   ├── repository.go
│   │   └── mysql_repository.go
│   ├── dto/
│   │   └── discount_dto.go
│   └── migrations/
│       └── 001_create_discounts.sql
```

## Plugins cần thiết

```
plugins/
├── payment/                            # Plugins thanh toán
│   ├── stripe/
│   │   ├── plugin.go                   # Khai báo plugin
│   │   ├── handler.go                  # API Handlers
│   │   └── service.go                  # Xử lý payment với Stripe
│   │
│   ├── paypal/
│   │   ├── plugin.go
│   │   ├── handler.go
│   │   └── service.go
│   │
│   ├── momo/                           # Plugin thanh toán MoMo (phổ biến ở VN)
│   │   ├── plugin.go
│   │   ├── handler.go
│   │   └── service.go
│   │
│   └── vnpay/                          # Plugin thanh toán VNPay (phổ biến ở VN)
│       ├── plugin.go
│       ├── handler.go
│       └── service.go
│
├── social/                             # Plugins social login
│   ├── google/
│   │   ├── plugin.go
│   │   ├── handler.go
│   │   └── service.go
│   │
│   └── facebook/
│       ├── plugin.go
│       ├── handler.go
│       └── service.go
```

## Cấu hình Dự án

Để kết hợp blog và e-commerce, cấu hình dự án có thể như sau:

```
projects/
├── blog-site/                          # Dự án blog đơn thuần
│   ├── config.yaml                     # Cấu hình ứng dụng
│   └── modules.yaml                    # Kích hoạt auth, user, blog
│
├── ecommerce/                          # Dự án thương mại điện tử
│   ├── config.yaml
│   └── modules.yaml                    # Kích hoạt auth, user, product, cart, order...
│
└── blog-ecommerce/                     # Dự án kết hợp blog và thương mại điện tử
    ├── config.yaml
    └── modules.yaml                    # Kích hoạt cả blog và e-commerce modules
```

### Ví dụ cấu hình modules.yaml cho dự án blog-ecommerce

```yaml
# Danh sách module được kích hoạt
modules:
  # Core modules
  - name: auth
    enabled: true
    config:
      jwt_secret: "your-secret-key"
      token_expiry: 3600
  
  - name: user
    enabled: true
    
  # Blog modules
  - name: blog
    enabled: true
    config:
      posts_per_page: 10
      
  # E-commerce modules
  - name: product
    enabled: true
    config:
      pagination_limit: 20
      
  - name: cart
    enabled: true
    config:
      expiry_hours: 72
      
  - name: order
    enabled: true
    
  - name: shipping
    enabled: true
    config:
      default_shipping_fee: 30000
      
  - name: discount
    enabled: true

# Danh sách plugin được kích hoạt
plugins:
  # Payment plugins
  - name: payment-momo
    enabled: true
    config:
      api_key: "momo-api-key"
      secret_key: "momo-secret-key"
      
  - name: payment-vnpay
    enabled: true
    config:
      terminal_id: "vnpay-terminal-id"
      secret_key: "vnpay-secret-key"
      
  # Social login plugins
  - name: social-google
    enabled: true
    config:
      client_id: "google-client-id"
      client_secret: "google-client-secret"
      
  - name: social-facebook
    enabled: true
    config:
      app_id: "facebook-app-id"
      app_secret: "facebook-app-secret"
```

## Lợi ích của cấu trúc này

1. **Mô-đun hóa hoàn toàn**: Mỗi thành phần là module độc lập có thể được kích hoạt/vô hiệu hóa theo nhu cầu dự án.

2. **Tách biệt mối quan tâm**: Mỗi module có responsibility rõ ràng với các layer riêng biệt (API, domain, repository).

3. **Khả năng mở rộng cao**: Dễ dàng thêm các module mới như phân tích (analytics), đánh giá sản phẩm (reviews), quản lý nội dung (CMS)...

4. **Tích hợp linh hoạt**: Plugins cho phép tích hợp với các dịch vụ bên thứ ba mà không cần sửa đổi mã nguồn core.

5. **Đa dự án**: Cùng một codebase có thể triển khai cho nhiều dự án khác nhau chỉ bằng cách thay đổi cấu hình.

6. **Khả năng bảo trì cao**: Mỗi module có thể được bảo trì và nâng cấp độc lập mà ít ảnh hưởng đến các module khác.

7. **Tối ưu hóa tài nguyên**: Khi triển khai, chỉ những module cần thiết mới được kích hoạt, giúp tối ưu hóa hiệu suất và tài nguyên.

Cấu trúc này phù hợp với cả các ứng dụng quy mô nhỏ đến lớn, và cho phép mở rộng dần dần khi nhu cầu kinh doanh phát triển.